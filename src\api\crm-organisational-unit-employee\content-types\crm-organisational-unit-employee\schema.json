{"kind": "collectionType", "collectionName": "crm_organisational_unit_employees", "info": {"singularName": "crm-organisational-unit-employee", "pluralName": "crm-organisational-unit-employees", "displayName": "CRM Organisational Unit Employee"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"reporting_line_indicator": {"type": "boolean"}, "role_code": {"type": "integer"}, "end_date": {"type": "date"}, "start_date": {"type": "date"}, "functional_indicator": {"type": "boolean"}, "organisational_unit_id": {"type": "string"}, "organisational_unit": {"type": "relation", "relation": "manyToOne", "target": "api::crm-organisational-unit.crm-organisational-unit", "inversedBy": "crm_org_unit_employees"}, "business_partner_internal_id": {"type": "string"}, "business_partner": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "crm_org_unit_employees"}, "employee_id": {"type": "string"}, "employee_external_key": {"type": "string"}, "job_id": {"type": "string"}, "job_external_key": {"type": "string"}}}