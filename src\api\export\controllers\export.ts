import * as fs from "fs";
import * as fsPromises from "fs/promises";
import * as path from "path";
import archiver from "archiver";
import { Workbook } from "exceljs";
import { Readable } from "stream";
import { AsyncParser } from "@json2csv/node";
import { Context } from "koa";
import { stringify } from "csv-stringify";
import { PassThrough } from "stream";

const exportDataAsCSV = async (ctx: Context) => {
  try {
    const data = ctx.request.body || [];

    if (!Array.isArray(data) || data.length === 0) {
      return ctx.badRequest("Invalid or empty data");
    }

    const parser = new AsyncParser();
    const csv = await parser.parse(data).promise();

    ctx.set("Content-Type", "text/csv");
    ctx.set("Content-Disposition", 'attachment; filename="cart.csv"');

    ctx.send(csv);
  } catch (error) {
    ctx.throw(500, "Failed to generate CSV");
  }
};

const exportData = async (ctx: any, tableName: string, id?: string) => {
  const modelName = tableName.toLowerCase().replace(/_/g, "-");
  const exportDir = path.resolve("export");
  const uniqueId = Date.now();
  const fileName = `${modelName}-${uniqueId}`;
  const zipFile = path.join(exportDir, `${fileName}.zip`);
  const batchSize = 1000;

  try {
    // Ensure the directory exists
    await fsPromises.mkdir(exportDir, { recursive: true });

    // Create a writable stream for the ZIP file
    const zipStream = fs.createWriteStream(zipFile);
    const archive = archiver("zip", { zlib: { level: 9 } });

    archive.pipe(zipStream);

    // Create a new Excel workbook and worksheet
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet("Data Export");

    let offset = 0;
    let hasMore = true;

    // Stream Excel file into the ZIP file
    while (hasMore) {
      let where: Record<string, any> = {};
      if (modelName === "import-file-log" && id) {
        where.import_file_id = id;
      }

      const records = await strapi
        .query(`api::${modelName}.${modelName}`)
        .findMany({
          where,
          limit: batchSize,
          offset,
        });

      if (offset === 0 && records.length > 0) {
        const excludeField: string[] = [
          "documentId",
          "updatedAt",
          "publishedAt",
          "createdBy",
          "updatedBy",
          "locale",
          "localizations",
        ];

        switch (modelName) {
          case "fg-control-main":
          case "fg-customer-business":
          case "fg-product-business":
          case "fg-relationship":
            excludeField.push("id");
            excludeField.push("createdAt");
            break;
          default:
        }

        const headers = Object.keys(records[0]).filter(
          (field) => !excludeField.includes(field)
        );
        worksheet.columns = headers.map((header) => ({
          header,
          key: header,
          width: 20,
        }));
      }

      // Write records in batches to Excel
      for (const record of records) {
        worksheet.addRow(record);
      }

      hasMore = records.length === batchSize;
      offset += batchSize;
    }

    // Create a buffer for the Excel file content
    const excelBuffer = await workbook.xlsx.writeBuffer();

    // Convert the buffer to a readable stream and pipe it to the zip archive
    const bufferStream = new Readable();
    bufferStream.push(excelBuffer);
    bufferStream.push(null); // End the stream

    // Append the buffer stream to the archive as an Excel file
    archive.append(bufferStream, { name: `${fileName}.xlsx` });

    // Finalize the archive and close the stream
    await archive.finalize();

    // Send the zip file to the client
    ctx.response.attachment(`${fileName}.zip`); // Set the file name for download
    ctx.response.type = "application/zip"; // Set the MIME type for ZIP files

    // Stream the ZIP file to the client
    ctx.body = fs.createReadStream(zipFile);

    // Defer file deletion until the response stream is closed
    ctx.body.on("close", async () => {
      await fsPromises.unlink(zipFile).catch((err) => {
        console.error("Error deleting zip file:", err);
      });
    });
  } catch (error) {
    console.error("Error exporting data:", error);
    ctx.throw(500, "Failed to export data");
  }
};

const exportProductWebAttributeCSV = async (ctx: Context) => {
  try {
    ctx.set(
      "Content-disposition",
      "attachment; filename=product_web_attributes.csv"
    );
    ctx.set("Content-Type", "text/csv");

    const stream = stringify({
      header: true,
      delimiter: "|",
      columns: [
        "code",
        "creationSystem",
        "name",
        "description",
        "productType",
        "itemsPerSalesUnit",
        "color",
        "size",
        "style",
        "capacity",
        "livingGreen",
        "threadCount",
        "fiberContent",
        "fillType",
        "fillWeight",
        "manufacturerPartNumber",
        "adaCompliant",
        "supercategories",
        "parentProduct",
        "variantType",
        "variantSort",
        "variantPlpName",
        "sapHierarchy1",
        "sapHierarchy2",
        "sapHierarchy3",
        "sapHierarchy4",
        "sapHierarchy5",
      ],
      quote: true,
    });

    const passThrough = new PassThrough();
    stream.pipe(passThrough);
    ctx.body = passThrough;

    const batchSize = 1000;
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const products = await strapi.db.query("api::product.product").findMany({
        offset,
        limit: batchSize,
        populate: ["categories"],
      });

      if (!products.length) {
        hasMore = false;
        break;
      }

      for (const product of products) {
        const row = {
          code: product.product_id || "",
          creationSystem: product.creationSystem || "",
          name: product.name || "",
          description: product.description || "",
          productType: product.productType || "",
          itemsPerSalesUnit: "",
          color: product.color || "",
          size: product.size || "",
          style: product.style || "",
          capacity: product.capacity || "",
          livingGreen: product.livingGreen ?? "",
          threadCount: product.threadCount || "",
          fiberContent: product.fiberContent || "",
          fillType: product.fillType || "",
          fillWeight: product.fillWeight || "",
          manufacturerPartNumber: "",
          adaCompliant: product.adaCompliant ?? "",
          supercategories:
            product.categories?.map((cat) => cat.name).join(", ") || "",
          parentProduct: product.parentProduct || "",
          variantType: product.ah_variant_type || "",
          variantSort: product.variantSort || "",
          variantPlpName: product.variantPlpName || "",
          sapHierarchy1: product.sapHierarchy1 || "",
          sapHierarchy2: product.sapHierarchy2 || "",
          sapHierarchy3: product.sapHierarchy3 || "",
          sapHierarchy4: product.sapHierarchy4 || "",
          sapHierarchy5: product.sapHierarchy5 || "",
        };

        stream.write(row);
      }

      offset += batchSize;
    }

    stream.end();
  } catch (err) {
    console.error("CSV Export Error:", err);
    ctx.status = 500;
    ctx.body = "Failed to export CSV.";
  }
};

const exportProductSpecificationsCSV = async (ctx: Context) => {
  try {
    ctx.set("Content-disposition", "attachment; filename=specifications.csv");
    ctx.set("Content-Type", "text/csv");

    const stream = stringify({
      header: true,
      delimiter: "|",
      columns: [
        "code",
        "adaCompliant",
        "hazmatCode",
        "sds",
        "livingGreen",
        "ulApproved",
        "culApproved",
        "prop65",
        "prop65Chemical",
        "prop65ChemType",
        "energyStar",
        "warranty",
        "warrantyType",
        "certification",
        "productWeight",
        "productLength",
        "productHeight",
        "productWidth",
        "shippingWeight",
        "shippingLength",
        "shippingHeight",
        "shippingWidth",
        "flavor",
        "coating",
        "shape",
        "applicableMaterial",
        "mount",
        "sealType",
        "cleaner",
        "productForm",
        "scent",
        "concentrated",
        "density",
        "gauge",
        "material",
        "closureType",
        "numberOfShelves",
        "doorStyle",
        "piecePerSet",
        "numberOfPlys",
        "sheetCount",
        "amperage",
        "hetz",
        "voltage",
        "wattage",
        "eel",
        "heat",
        "heatBTU",
        "coolBTU",
        "refrigerantType",
        "averageCoveringArea",
        "noiseLevel",
        "powerSource",
        "plugType",
        "outlet",
        "cordLength",
        "filter",
        "lbsPerDozen",
        "hemColor",
        "pocketDepth",
        "gsm",
        "hoseLength",
        "cleaningPath",
        "oekoTex",
        "fscCertified",
        "greenSealCertified",
        "diversitySupplier",
        "manufacturerName",
        "truckOnly",
        "pfas",
        "brandName",
        "diversityCertificate",
        "guage",
        "hertz",
        "refigerantType",
        "originCountry",
        "shippingUnit",
      ],
      quote: true,
    });

    const passThrough = new PassThrough();
    stream.pipe(passThrough);
    ctx.body = passThrough;

    const batchSize = 1000;
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const products = await strapi.db.query("api::product.product").findMany({
        offset,
        limit: batchSize,
        populate: ["categories"],
      });

      if (!products.length) {
        hasMore = false;
        break;
      }

      for (const product of products) {
        const row = {
          code: product.product_id || "",
          adaCompliant: product.adaCompliant ?? "",
          hazmatCode: product.hazmatCode || "",
          sds: product.sds || "",
          livingGreen: product.livingGreen ?? "",
          ulApproved: product.ulApproved ?? "",
          culApproved: product.culApproved ?? "",
          prop65: product.prop65 ?? "",
          prop65Chemical: product.prop65Chemical || "",
          prop65ChemType: product.prop65ChemType || "",
          energyStar: product.energyStar ?? "",
          warranty: product.warranty || "",
          warrantyType: product.warrantyType || "",
          certification: product.certification || "",
          productWeight: product.productWeight || "",
          productLength: product.productLength || "",
          productHeight: product.productHeight || "",
          productWidth: product.productWidth || "",
          shippingWeight: product.shippingWeight || "",
          shippingLength: product.shippingLength || "",
          shippingHeight: product.shippingHeight || "",
          shippingWidth: product.shippingWidth || "",
          flavor: product.flavor || "",
          coating: product.coating || "",
          shape: product.shape || "",
          applicableMaterial: product.applicableMaterial || "",
          mount: product.mount || "",
          sealType: product.sealType || "",
          cleaner: product.cleaner || "",
          productForm: product.productForm || "",
          scent: product.scent || "",
          concentrated: product.concentrated ?? "",
          density: product.density || "",
          gauge: product.gauge || "",
          material: product.material || "",
          closureType: product.closureType || "",
          numberOfShelves: product.numberOfShelves || "",
          doorStyle: product.doorStyle || "",
          piecePerSet: product.piecePerSet || "",
          numberOfPlys: product.numberOfPlys || "",
          sheetCount: product.sheetCount || "",
          amperage: product.amperage || "",
          hetz: product.hetz || "",
          voltage: product.voltage || "",
          wattage: product.wattage || "",
          eel: product.eel || "",
          heat: product.heat ?? "",
          heatBTU: product.heatBTU || "",
          coolBTU: product.coolBTU || "",
          refrigerantType: product.refrigerantType || "",
          averageCoveringArea: product.averageCoveringArea || "",
          noiseLevel: product.noiseLevel || "",
          powerSource: product.powerSource || "",
          plugType: product.plugType || "",
          outlet: product.outlet || "",
          cordLength: product.cordLength || "",
          filter: product.filter || "",
          lbsPerDozen: product.lbsPerDozen || "",
          hemColor: product.hemColor || "",
          pocketDepth: product.pocketDepth || "",
          gsm: product.gsm || "",
          hoseLength: product.hoseLength || "",
          cleaningPath: product.cleaningPath || "",
          oekoTex: product.oekoTex ?? "",
          fscCertified: product.fscCertified ?? "",
          greenSealCertified: product.greenSealCertified ?? "",
          diversitySupplier: product.diversitySupplier ?? "",
          manufacturerName: product.manufacturerName ?? "",
          truckOnly: product.truckOnly ?? "",
          pfas: product.pfas ?? "",
          brandName: product.brandName ?? "",
          diversityCertificate: product.diversityCertificate ?? "",
          guage: product.guage ?? "",
          hertz: product.hertz ?? "",
          refigerantType: product.refigerantType ?? "",
          originCountry: product.originCountry ?? "",
          shippingUnit: product.shippingUnit ?? "",
        };

        stream.write(row);
      }

      offset += batchSize;
    }

    stream.end();
  } catch (err) {
    console.error("CSV Export Error:", err);
    ctx.status = 500;
    ctx.body = "Failed to export CSV.";
  }
};

const exportFGControlMainText = async (ctx: Context) => {
  try {
    ctx.set("Content-disposition", "attachment; filename=fg_control_main.txt");
    ctx.set("Content-Type", "text/plain");

    const stream = stringify({
      header: true,
      delimiter: "|",
      columns: [
        "material",
        "group",
        "type",
        "usage",
        "name",
        "icon",
        "iconText",
      ],
      quote: true,
    });

    const passThrough = new PassThrough();
    stream.pipe(passThrough);
    ctx.body = passThrough;

    const batchSize = 1000;
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      // Fetch flex groups in batches
      const fgCtrlMains = await strapi.db
        .query("api::fg-control-main.fg-control-main")
        .findMany({
          offset,
          limit: batchSize,
        });

      if (!fgCtrlMains.length) {
        hasMore = false;
        break;
      }

      // Process each flex group
      for (const fgCtrlMain of fgCtrlMains) {
        const flexGroupId = fgCtrlMain.flex_group_id;
        let productOffset = 0;
        let hasMoreProducts = true;

        // Process products in chunks for this flex group
        while (hasMoreProducts) {
          const products = await strapi.db
            .query("api::fg-product-internal.fg-product-internal")
            .findMany({
              where: { flex_group_id: flexGroupId },
              offset: productOffset,
              limit: batchSize,
            });

          if (!products.length) {
            hasMoreProducts = false;
            break;
          }

          // Write each product to CSV
          for (const product of products) {
            const group =
              fgCtrlMain?.flex_group_id?.toString()?.padStart(10, "0") || "";
            const row: any = {
              material: product.product_id || "",
              group,
              type: fgCtrlMain.flex_group_type || "",
              usage: fgCtrlMain.indicator_type || "",
              name: fgCtrlMain.description || "",
              icon: fgCtrlMain.icon_image || "",
              iconText: fgCtrlMain.icon_text || "",
            };

            stream.write(row);
          }

          productOffset += batchSize;
        }
      }

      offset += batchSize;
    }

    stream.end();
  } catch (err) {
    console.error("Text File Export Error:", err);
    ctx.status = 500;
    ctx.body = "Failed to export Text File.";
  }
};

module.exports = {
  async fgControlMains(ctx) {
    await exportData(ctx, "FG_CONTROL_MAIN");
  },

  async fgCustomerBusinesses(ctx) {
    await exportData(ctx, "FG_CUSTOMER_BUSINESS");
  },

  async fgCustomerInternals(ctx) {
    await exportData(ctx, "FG_CUSTOMER_INTERNAL");
  },

  async fgProductBusinesses(ctx) {
    await exportData(ctx, "FG_PRODUCT_BUSINESS");
  },

  async fgProductInternals(ctx) {
    await exportData(ctx, "FG_PRODUCT_INTERNAL");
  },

  async fgRelationships(ctx) {
    await exportData(ctx, "FG_RELATIONSHIP");
  },

  async importFileLogs(ctx) {
    const { fileStateDocId } = ctx.params;
    await exportData(ctx, "IMPORT-FILE-LOG", fileStateDocId);
  },

  async cart(ctx) {
    await exportDataAsCSV(ctx);
  },

  async productWebAttribute(ctx) {
    await exportProductWebAttributeCSV(ctx);
  },

  async productSpecifications(ctx) {
    await exportProductSpecificationsCSV(ctx);
  },

  async fgControlMainText(ctx) {
    await exportFGControlMainText(ctx);
  },
};
