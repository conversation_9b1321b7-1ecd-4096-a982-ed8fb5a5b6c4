import { generateUniqueID } from "../../../../utils/cpi";

export default {
  async beforeCreate(event) {
    const { params } = event;
    if (!params.data.opportunity_id) {
      try {
        const customId = `${await generateUniqueID("crm_organisational_unit_id_seq")}`;
        params.data.organisational_unit_id = customId;
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
};
