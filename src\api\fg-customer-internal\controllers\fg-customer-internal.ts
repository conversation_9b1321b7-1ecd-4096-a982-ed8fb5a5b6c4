/**
 * fg-customer-internal controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";
import { chunkArray, syncBulkCustomerInternal } from "./helpers";

export default factories.createCoreController(
  "api::fg-customer-internal.fg-customer-internal",
  ({ strapi }) => ({
    async sync(ctx: Context) {
      const setting = await strapi.db.query("api::setting.setting").findOne({
        select: ["id", "is_fgci_in_progress"],
      });

      if (!setting) {
        return ctx.badRequest("No active sync found.");
      }

      if (setting.is_fgci_in_progress) {
        return ctx.conflict("Sync is already in progress.");
      }

      // Set flag to prevent multiple requests
      await strapi.db.query("api::setting.setting").update({
        where: { id: setting.id },
        data: { is_fgci_in_progress: true },
      });

      setImmediate(async () => {
        try {
          console.log("Backup FG Customer Internal records...");

          await strapi.db.connection.raw(`
            -- Clear existing backup
            TRUNCATE TABLE fg_customer_internal_backups;
            -- Insert backup data
            INSERT INTO fg_customer_internal_backups SELECT * FROM fg_customer_internals;
          `);

          console.log("DONE Backup FG Customer Internal records...");

          console.log("Fetching FG Customer Business records...");

          let offset = 0;
          const limit = 1000;

          // Delete Records
          while (true) {
            const bps = await strapi.db
              .query("api::fg-customer-business.fg-customer-business")
              .findMany({
                where: {
                  flex_group_id: { $ne: null },
                  is_all_bp: { $eq: false },
                  $or: [
                    { bp_id: { $ne: null } },
                    { membership_id: { $ne: null } },
                  ],
                  operand: "DELETE",
                },
                select: [
                  "id",
                  "flex_group_id",
                  "bp_id",
                  "membership_id",
                  "locale",
                ],
                limit,
                offset,
              });

            if (!bps.length) break;

            console.log(`Processing delete batch of ${bps.length} records...`);

            const chunkedBps = chunkArray(bps, 500);
            for (const chunk of chunkedBps) {
              for (const result of chunk) {
                if (result.flex_group_id && result.bp_id) {
                  const toDelete = await strapi.db
                    .query("api::fg-customer-internal.fg-customer-internal")
                    .findMany({
                      where: {
                        flex_group_id: result.flex_group_id,
                        bp_id: result.bp_id,
                        locale: result.locale || "en",
                      },
                      select: ["id"],
                    });

                  // Perform bulk deletion using the IDs
                  if (toDelete.length > 0) {
                    await strapi.db
                      .query("api::fg-customer-internal.fg-customer-internal")
                      .deleteMany({
                        where: { id: { $in: toDelete.map(({ id }) => id) } },
                      });
                  }
                }

                if (result.flex_group_id && result.membership_id) {
                  const bpIDs = await strapi.db
                    .query(
                      "api::business-partner-relationship.business-partner-relationship"
                    )
                    .findMany({
                      where: {
                        bp_id2: { $eq: result.membership_id },
                      },
                      select: ["bp_id1", "locale"],
                    });

                  const toDelete = await strapi.db
                    .query("api::fg-customer-internal.fg-customer-internal")
                    .findMany({
                      where: {
                        flex_group_id: result.flex_group_id,
                        bp_id: { $in: bpIDs?.map(({ bp_id1 }) => bp_id1) },
                        locale: result.locale || "en",
                      },
                      select: ["id"],
                    });

                  // Perform bulk deletion using the IDs
                  if (toDelete.length > 0) {
                    await strapi.db
                      .query("api::fg-customer-internal.fg-customer-internal")
                      .deleteMany({
                        where: { id: { $in: toDelete.map(({ id }) => id) } },
                      });
                  }
                }

                if (result?.id) {
                  await strapi.db
                    .query("api::fg-customer-business.fg-customer-business")
                    .delete({
                      where: { id: result.id },
                    });
                }
              }
            }

            offset += limit;
          }

          console.log(
            "Initial batch deletion completed. Processing batch insert data..."
          );

          offset = 0;

          while (true) {
            const bps = await strapi.db
              .query("api::fg-customer-business.fg-customer-business")
              .findMany({
                where: {
                  flex_group_id: { $ne: null },
                  bp_id: { $ne: null },
                  is_all_bp: { $eq: false },
                  $or: [{ operand: "ADD" }, { operand: "UPDATE" }],
                },
                select: ["id", "flex_group_id", "bp_id", "locale"],
                limit,
                offset,
              });

            if (!bps.length) break;

            console.log(`Processing batch of ${bps.length} records...`);

            const chunkedBps = chunkArray(bps, 500);
            for (const chunk of chunkedBps) {
              await syncBulkCustomerInternal(chunk);
            }

            offset += limit;
          }

          console.log(
            "Initial batch insert completed. Processing membership data..."
          );

          offset = 0;

          while (true) {
            const membershipBPs = await strapi.db
              .query("api::fg-customer-business.fg-customer-business")
              .findMany({
                where: {
                  flex_group_id: { $ne: null },
                  membership_id: { $ne: null },
                  is_all_bp: { $eq: false },
                  $or: [{ operand: "ADD" }, { operand: "UPDATE" }],
                },
                select: ["id", "flex_group_id", "membership_id", "locale"],
                limit,
                offset,
              });

            if (!membershipBPs.length) break;

            console.log(
              `Processing membership batch of ${membershipBPs.length} records...`
            );

            const bulkInsertData = [];
            for (const data of membershipBPs) {
              try {
                const bpIDs = await strapi.db
                  .query(
                    "api::business-partner-relationship.business-partner-relationship"
                  )
                  .findMany({
                    where: {
                      bp_id2: { $eq: data.membership_id },
                    },
                    select: ["bp_id1", "locale"],
                  });

                console.log(`Processing bp ${bpIDs.length} entries found`);

                for (const bp_id of bpIDs.map((o: any) => o.bp_id1)) {
                  bulkInsertData.push({
                    flex_group_id: data.flex_group_id,
                    bp_id,
                    locale: data.locale || "en",
                  });
                }
              } catch (error) {
                console.error("Error processing membership record:", error);
              }
            }

            if (bulkInsertData.length > 0) {
              const chunkedBulkData = chunkArray(bulkInsertData, 500);
              for (const chunk of chunkedBulkData) {
                await syncBulkCustomerInternal(chunk);
              }
            }

            offset += limit;
          }
        } catch (error) {
          console.error("Sync error:", error);
        } finally {
          // Ensure the flag is reset even if an error occurs
          await strapi.db.query("api::setting.setting").update({
            where: { id: setting.id },
            data: { is_fgci_in_progress: false },
          });
        }
      });

      return ctx.send({ message: "Sync started." });
    },
    async exportCustomerInternals(ctx: Context) {
      const { customerId } = ctx.params;

      if (!customerId) {
        return ctx.throw(400, "Customer Id required");
      }

      const batchSize = 500;
      let isFirstItem = true;

      ctx.set("Content-Type", "application/json");
      ctx.set("Transfer-Encoding", "chunked");
      ctx.status = 200;

      const { PassThrough } = require("stream");
      const stream = new PassThrough();
      ctx.body = stream;

      const processChunks = async (queryName: string, whereCondition: any) => {
        let offset = 0;
        let hasMore = true;

        while (hasMore) {
          const results = await strapi.db.query(queryName).findMany({
            where: whereCondition,
            offset,
            limit: batchSize,
          });

          if (!results.length) {
            hasMore = false;
            break;
          }

          for (const result of results) {
            const flexGroupId = result.flex_group_id;

            const fgCtrlMain = await strapi.db
              .query("api::fg-control-main.fg-control-main")
              .findOne({
                where: { flex_group_id: flexGroupId },
              });

            const row = {
              flexiblegroupid: fgCtrlMain.flex_group_id
                ? fgCtrlMain.flex_group_id.toString().padStart(10, "0")
                : "",
              flexiblegroupname: fgCtrlMain?.description || "",
              flexiblegrouptype: fgCtrlMain?.flex_group_type || "",
            };

            if (!isFirstItem) stream.write(",");
            isFirstItem = false;

            stream.write(JSON.stringify(row));
          }

          offset += batchSize;
        }
      };

      (async () => {
        stream.write('{"flexiblegroups": [');

        await processChunks("api::fg-customer-internal.fg-customer-internal", {
          bp_id: customerId,
        });

        await processChunks("api::fg-customer-business.fg-customer-business", {
          is_all_bp: true,
        });

        stream.write("]}");
        stream.end();
      })().catch((err) => {
        console.error("Streaming error:", err);
        stream.end();
      });
    },
    async restoreCustomerInternalData(ctx: Context) {
      try {
        console.log("Restore FG Customer Internal records...");

        await strapi.db.connection.raw(`
            -- Clear existing backup
            TRUNCATE TABLE fg_customer_internals;
            -- Insert backup data
            INSERT INTO fg_customer_internals SELECT * FROM fg_customer_internal_backups;
          `);

        console.log("DONE Restore FG Customer Internal records...");
        return ctx.send({ message: "Restore done." });
      } catch (error) {
        console.log("Error Restore FG Customer Internal records...", error);
      }
    },
  })
);
