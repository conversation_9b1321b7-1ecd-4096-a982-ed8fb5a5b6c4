import {
  chunkArray,
  syncBulkCustomerInternal,
  fetchProductHierarchy,
  syncBulkProductInternal,
  uploadFGControlMainTextToAzure,
  uploadProductWebAttributeToAzure,
  uploadProductSpecificationToAzure,
  uploadProductMediaToAzure,
} from "./helpers";

export default {
  /**
   * Delete Expired Guest User Carts.
   * Runs every day at midnight
   */

  deleteExpiredCarts: {
    task: async ({ strapi }) => {
      const expiredCarts = await strapi
        .query("api::guest-user-cart.guest-user-cart")
        .findMany({
          where: {
            expire_at: { $lt: new Date() },
          },
        });

      // Delete expired carts and their items
      await Promise.all(
        expiredCarts.map(async (cart) => {
          await strapi
            .documents("api::guest-user-cart.guest-user-cart")
            .delete({
              documentId: cart.documentId,
            });
        })
      );
    },
    options: {
      rule: "0 0 * * *",
      tz: "America/Chicago",
    },
  },

  /**
   * Scheduler for Azure upload.
   * Runs every day at midnight
   */
  uploadToAzure: {
    task: async ({ strapi }) => {
      await uploadFGControlMainTextToAzure();
      await uploadProductWebAttributeToAzure();
      await uploadProductSpecificationToAzure();
      await uploadProductMediaToAzure();
    },
    options: {
      rule: "0 0 * * *",
      tz: "America/Chicago",
    },
  },

  /**
   * Scheduler for segregate the data.
   * Runs every day at midnight
   */
  // scheduler: {
  //   task: async ({ strapi }) => {
  //     const result = await strapi.db.connection.raw(`
  //         SELECT s.*
  //         FROM schedulers AS s
  //         WHERE s.is_active=true AND s.start_date <= CURRENT_DATE
  //           AND CURRENT_DATE <= s.end_date
  //           AND (
  //             s.schedule_type = 'DAILY'
  //             OR (
  //               s.schedule_type = 'MONTHLY'
  //               AND EXTRACT(DAY FROM CURRENT_DATE) = s.day_of_month
  //             )
  //             OR (
  //               s.schedule_type = 'WEEKLY'
  //               AND EXISTS (
  //                 SELECT 1
  //                 FROM unnest(string_to_array(s.weekdays_to_generate, ', ')) AS weekday
  //                 WHERE
  //                   (weekday = 'MONDAY' AND EXTRACT(DOW FROM CURRENT_DATE) = 1) OR
  //                   (weekday = 'TUESDAY' AND EXTRACT(DOW FROM CURRENT_DATE) = 2) OR
  //                   (weekday = 'WEDNESDAY' AND EXTRACT(DOW FROM CURRENT_DATE) = 3) OR
  //                   (weekday = 'THURSDAY' AND EXTRACT(DOW FROM CURRENT_DATE) = 4) OR
  //                   (weekday = 'FRIDAY' AND EXTRACT(DOW FROM CURRENT_DATE) = 5) OR
  //                   (weekday = 'SATURDAY' AND EXTRACT(DOW FROM CURRENT_DATE) = 6) OR
  //                   (weekday = 'SUNDAY' AND EXTRACT(DOW FROM CURRENT_DATE) = 0)
  //               )
  //             )
  //           );
  //       `);

  //     if (result.rows.length > 0) {
  //       const schedulers = result.rows || [];

  //       const isFgCustomerBusinessScheduled = schedulers.some(
  //         (s: any) => s.operation === "FG_CUSTOMER_BUSINESS"
  //       );
  //       if (isFgCustomerBusinessScheduled) {
  //         console.log("Fetching FG Customer Business records...");

  //         // Fetch all records from fg-product-business in chunks
  //         let offset = 0;
  //         const limit = 1000; // Batch size (adjust as needed)

  //         while (true) {
  //           const bps = await strapi.db
  //             .query("api::fg-customer-business.fg-customer-business")
  //             .findMany({
  //               where: {
  //                 flex_group_id: { $ne: null },
  //                 bp_id: { $ne: null },
  //               },
  //               select: ["id", "flex_group_id", "bp_id", "locale"],
  //               limit,
  //               offset,
  //             });

  //           if (!bps.length) break;

  //           console.log(`Processing batch of ${bps.length} records...`);

  //           // Process & insert in bulk
  //           const chunkeBbps = chunkArray(bps, 500);
  //           for (const chunk of chunkeBbps) {
  //             await syncBulkCustomerInternal(chunk);
  //           }

  //           offset += limit;
  //         }

  //         console.log(
  //           "Initial batch insert completed. Processing membership data..."
  //         );

  //         // Fetch hierarchical data and process in bulk
  //         offset = 0;

  //         while (true) {
  //           const membershipBPs = await strapi.db
  //             .query("api::fg-customer-business.fg-customer-business")
  //             .findMany({
  //               where: {
  //                 flex_group_id: { $ne: null },
  //                 membership_id: { $ne: null },
  //               },
  //               select: ["id", "flex_group_id", "membership_id", "locale"],
  //               limit,
  //               offset,
  //             });

  //           if (!membershipBPs.length) break;

  //           console.log(
  //             `Processing membership batch of ${membershipBPs.length} records...`
  //           );

  //           const bulkInsertData = [];
  //           for (const data of membershipBPs) {
  //             try {
  //               const bpIDs = await strapi.db
  //                 .query(
  //                   "api::business-partner-relationship.business-partner-relationship"
  //                 )
  //                 .findMany({
  //                   where: {
  //                     bp_id2: { $eq: data.membership_id },
  //                   },
  //                   select: ["bp_id1", "locale"],
  //                 });

  //               console.log(`Processing bp ${bpIDs.length} entries found`);

  //               for (const bp_id of bpIDs.map((o: any) => o.bp_id1)) {
  //                 bulkInsertData.push({
  //                   flex_group_id: data.flex_group_id,
  //                   bp_id,
  //                   locale: data.locale || "en",
  //                 });
  //               }
  //             } catch (error) {
  //               console.error("Error processing membership record:", error);
  //             }
  //           }

  //           if (bulkInsertData.length > 0) {
  //             const chunkedBulkData = chunkArray(bulkInsertData, 500);
  //             for (const chunk of chunkedBulkData) {
  //               await syncBulkCustomerInternal(chunk);
  //             }
  //           }

  //           offset += limit;
  //         }
  //       }

  //       const isFgProductBusinessScheduled = schedulers.some(
  //         (s: any) => s.operation === "FG_PRODUCT_BUSINESS"
  //       );

  //       if (isFgProductBusinessScheduled) {
  //         console.log("Fetching FG Product Business records...");

  //         // Fetch all records from fg-product-business in chunks
  //         let offset = 0;
  //         const limit = 1000; // Batch size (adjust as needed)

  //         while (true) {
  //           const products = await strapi.db
  //             .query("api::fg-product-business.fg-product-business")
  //             .findMany({
  //               where: {
  //                 flex_group_id: { $ne: null },
  //                 product_id: { $ne: null },
  //               },
  //               select: ["id", "flex_group_id", "product_id", "locale"],
  //               limit,
  //               offset,
  //             });

  //           if (!products.length) break;

  //           console.log(`Processing batch of ${products.length} records...`);

  //           // Process & insert in bulk
  //           const chunkedProducts = chunkArray(products, 500);
  //           for (const chunk of chunkedProducts) {
  //             await syncBulkProductInternal(chunk);
  //           }

  //           offset += limit;
  //         }

  //         console.log(
  //           "Initial batch insert completed. Processing hierarchical data..."
  //         );

  //         // Fetch hierarchical data and process in bulk
  //         offset = 0;

  //         while (true) {
  //           const hierarchicalProducts = await strapi.db
  //             .query("api::fg-product-business.fg-product-business")
  //             .findMany({
  //               where: {
  //                 flex_group_id: { $ne: null },
  //                 product_hierarchy: { $ne: null },
  //               },
  //               select: ["id", "flex_group_id", "product_hierarchy", "locale"],
  //               limit,
  //               offset,
  //             });

  //           if (!hierarchicalProducts.length) break;

  //           console.log(
  //             `Processing hierarchical batch of ${hierarchicalProducts.length} records...`
  //           );

  //           const bulkInsertData = [];
  //           for (const data of hierarchicalProducts) {
  //             try {
  //               const productIds = await fetchProductHierarchy(
  //                 data.product_hierarchy
  //               );
  //               console.log(
  //                 `Processing hierarchical ${productIds.length} entries found`
  //               );
  //               for (const product_id of productIds) {
  //                 bulkInsertData.push({
  //                   flex_group_id: data.flex_group_id,
  //                   product_id,
  //                   locale: data.locale || "en",
  //                 });
  //               }
  //             } catch (error) {
  //               console.error(
  //                 "Error processing product hierarchy record:",
  //                 error
  //               );
  //             }
  //           }

  //           if (bulkInsertData.length > 0) {
  //             const chunkedBulkData = chunkArray(bulkInsertData, 500);
  //             for (const chunk of chunkedBulkData) {
  //               await syncBulkProductInternal(chunk);
  //             }
  //           }

  //           offset += limit;
  //         }
  //       } else {
  //         console.log(`No schedulers for today.`);
  //       }
  //     }
  //   },
  //   options: {
  //     // rule: "*/1 * * * *", // Every minute
  //     rule: "0 0 * * *",
  //     tz: "America/Chicago",
  //   },
  // },
};
