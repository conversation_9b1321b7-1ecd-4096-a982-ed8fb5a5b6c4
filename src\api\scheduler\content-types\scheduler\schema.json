{"kind": "collectionType", "collectionName": "schedulers", "info": {"singularName": "scheduler", "pluralName": "schedulers", "displayName": "Scheduler"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"start_date": {"type": "datetime", "required": true}, "end_date": {"type": "datetime", "required": true}, "schedule_type": {"type": "enumeration", "enum": ["DAILY", "WEEKLY", "MONTHLY"], "default": "DAILY", "required": true}, "day_of_month": {"type": "integer", "min": 1, "max": 31}, "weekdays_to_generate": {"type": "string", "maxLength": 50, "description": "Comma-separated list of weekdays to run the task (e.g., 'MONDAY, WEDNESDAY')"}, "operation": {"type": "enumeration", "enum": ["FG_CUSTOMER_BUSINESS", "FG_PRODUCT_BUSINESS"], "required": true}, "is_active": {"type": "boolean", "default": false}}}