import { PassThrough } from "stream";
import { stringify } from "csv-stringify";
import {
  BlobServiceClient,
  StorageSharedKeyCredential,
} from "@azure/storage-blob";

const chunkArray = (array, size) => {
  return Array.from({ length: Math.ceil(array.length / size) }, (_, index) =>
    array.slice(index * size, index * size + size)
  );
};

const syncBulkCustomerInternal = async (dataBatch) => {
  try {
    if (!dataBatch || dataBatch.length === 0) return;

    const flexGroupIds = dataBatch.map((d) => d.flex_group_id);
    const bpIds = dataBatch.map((d) => d.bp_id);
    const locales = dataBatch.map((d) => d.locale || "en");

    // Step 1: Find existing records
    const existingRecords = await strapi.db
      .query("api::fg-customer-internal.fg-customer-internal")
      .findMany({
        where: {
          flex_group_id: { $in: flexGroupIds },
          bp_id: { $in: bpIds },
          locale: { $in: locales },
        },
        select: ["flex_group_id", "bp_id", "locale"],
      });

    // Step 2: Convert existing records into a Set for quick lookup
    const existingSet = new Set(
      existingRecords.map((r) => `${r.flex_group_id}-${r.bp_id}-${r.locale}`)
    );

    // Step 3: Filter out duplicates before inserting
    const bulkInsertData = dataBatch.filter(
      (d) => !existingSet.has(`${d.flex_group_id}-${d.bp_id}-${d.locale}`)
    );

    if (bulkInsertData.length > 0) {
      await strapi.db
        .query("api::fg-customer-internal.fg-customer-internal")
        .createMany({
          data: bulkInsertData,
        });
    }
  } catch (error) {
    console.error("Bulk insert error in syncBulkCustomerInternal:", error);
  }
};

const getHierarchy = async (node) => {
  const hierarchy = await strapi.entityService.findMany(
    "api::product-hierarchy.product-hierarchy",
    {
      filters: {
        $or: [{ parent_node: node }, { child_node: node }],
      },
      populate: {
        children: {
          populate: "children",
        },
      },
    }
  );

  return hierarchy;
};

const extractProductIds = (node, ids = []) => {
  ids.push(node.product_id);

  if (node.children && node.children.length > 0) {
    node.children.forEach((child) => {
      extractProductIds(child, ids);
    });
  }

  return ids;
};

const fetchProductHierarchy = async (node) => {
  const rootNode = await getHierarchy(node);

  if (!rootNode || rootNode.length === 0) {
    return [];
  }

  let allProductIds = [];
  rootNode.forEach((node) => {
    allProductIds = allProductIds.concat(extractProductIds(node));
  });

  return allProductIds;
};

const syncBulkProductInternal = async (dataBatch) => {
  try {
    if (!dataBatch || dataBatch.length === 0) return;

    const flexGroupIds = dataBatch.map((d) => d.flex_group_id);
    const productIds = dataBatch.map((d) => d.product_id);
    const locales = dataBatch.map((d) => d.locale || "en");

    // Step 1: Find existing records
    const existingRecords = await strapi.db
      .query("api::fg-product-internal.fg-product-internal")
      .findMany({
        where: {
          flex_group_id: { $in: flexGroupIds },
          product_id: { $in: productIds },
          locale: { $in: locales },
        },
        select: ["flex_group_id", "product_id", "locale"],
      });

    // Step 2: Convert existing records into a Set for quick lookup
    const existingSet = new Set(
      existingRecords.map(
        (r) => `${r.flex_group_id}-${r.product_id}-${r.locale}`
      )
    );

    // Step 3: Filter out duplicates before inserting
    const bulkInsertData = dataBatch.filter(
      (d) => !existingSet.has(`${d.flex_group_id}-${d.product_id}-${d.locale}`)
    );

    if (bulkInsertData.length > 0) {
      await strapi.db
        .query("api::fg-product-internal.fg-product-internal")
        .createMany({
          data: bulkInsertData,
        });
    }
  } catch (error) {
    console.error("Bulk insert error in syncBulkProductInternal:", error);
  }
};

const uploadFGControlMainTextToAzure = async () => {
  try {
    const passThrough = new PassThrough();

    // Create the CSV stream
    const stream = stringify({
      header: true,
      delimiter: "|",
      columns: [
        "material",
        "group",
        "type",
        "usage",
        "name",
        "icon",
        "iconText",
      ],
      quote: true,
    });

    // Pipe the CSV data to passThrough
    stream.pipe(passThrough);

    const accountName = process.env.AZURE_BLOB_ACCOUNT_NAME;
    const accountKey = process.env.AZURE_BLOB_ACCOUNT_KEY;
    const containerName = process.env.AZURE_BLOB_CONTAINER_NAME;
    const blobBasePath = process.env.AZURE_BLOB_FILE_PATH || "";

    // Generate timestamped filename
    const now = new Date();
    const timestamp = now
      .toISOString()
      .replace(/[-:.TZ]/g, "")
      .slice(0, 14);
    const fileName = `flexible_group-${timestamp}.csv`;
    const fullBlobPath = `${blobBasePath.replace(/\/$/, "")}/${fileName}`;

    const sharedKeyCredential = new StorageSharedKeyCredential(
      accountName,
      accountKey
    );
    const blobServiceClient = new BlobServiceClient(
      `https://${accountName}.blob.core.windows.net`,
      sharedKeyCredential
    );

    const containerClient = blobServiceClient.getContainerClient(containerName);

    const exists = await containerClient.exists();
    if (!exists) {
      strapi.log.info(
        `Container '${containerName}' does not exist. Creating it...`
      );
      await containerClient.create();
      strapi.log.info(`Container '${containerName}' created successfully.`);
    }

    const blockBlobClient = containerClient.getBlockBlobClient(fullBlobPath);

    const azureUploadPromise = blockBlobClient.uploadStream(
      passThrough,
      undefined,
      undefined,
      {
        blobHTTPHeaders: { blobContentType: "text/csv" },
      }
    );

    // Process data and write to the stream
    const batchSize = 1000;
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const fgCtrlMains = await strapi.db
        .query("api::fg-control-main.fg-control-main")
        .findMany({ offset, limit: batchSize });

      if (!fgCtrlMains.length) break;

      for (const fgCtrlMain of fgCtrlMains) {
        const flexGroupId = fgCtrlMain.flex_group_id;
        let productOffset = 0;
        let hasMoreProducts = true;

        while (hasMoreProducts) {
          const products = await strapi.db
            .query("api::fg-product-internal.fg-product-internal")
            .findMany({
              where: { flex_group_id: flexGroupId },
              offset: productOffset,
              limit: batchSize,
            });

          if (!products.length) break;

          for (const product of products) {
            const group =
              fgCtrlMain?.flex_group_id?.toString()?.padStart(10, "0") || "";
            stream.write({
              material: product.product_id || "",
              group,
              type: fgCtrlMain.flex_group_type || "",
              usage: fgCtrlMain.indicator_type || "",
              name: fgCtrlMain.description || "",
              icon: fgCtrlMain.icon_image || "",
              iconText: fgCtrlMain.icon_text || "",
            });
          }

          productOffset += batchSize;
        }
      }

      offset += batchSize;
    }

    // End the stream
    stream.end();

    // Wait for Azure upload to finish
    await azureUploadPromise;

    strapi.log.info(
      `FG Control Main export uploaded to Azure Blob Storage at '${fullBlobPath}'.`
    );
  } catch (err) {
    strapi.log.error("Error exporting/uploading FG Control Main:", err);
  }
};

const uploadProductWebAttributeToAzure = async () => {
  try {
    const passThrough = new PassThrough();

    // Create the CSV stream
    const stream = stringify({
      header: true,
      delimiter: "|",
      columns: [
        "code",
        "creationSystem",
        "name",
        "description",
        "productType",
        "itemsPerSalesUnit",
        "color",
        "size",
        "style",
        "capacity",
        "livingGreen",
        "threadCount",
        "fiberContent",
        "fillType",
        "fillWeight",
        "manufacturerPartNumber",
        "adaCompliant",
        "supercategories",
        "parentProduct",
        "variantType",
        "variantSort",
        "variantPlpName",
        "sapHierarchy1",
        "sapHierarchy2",
        "sapHierarchy3",
        "sapHierarchy4",
        "sapHierarchy5",
      ],
      quote: true,
    });

    // Pipe the CSV data to passThrough
    stream.pipe(passThrough);

    const accountName = process.env.AZURE_BLOB_ACCOUNT_NAME;
    const accountKey = process.env.AZURE_BLOB_ACCOUNT_KEY;
    const containerName = process.env.AZURE_BLOB_CONTAINER_NAME;
    const blobBasePath = process.env.AZURE_BLOB_FILE_PATH || "";

    // Generate timestamped filename
    const now = new Date();
    const timestamp = now
      .toISOString()
      .replace(/[-:.TZ]/g, "")
      .slice(0, 14);
    const fileName = `product_information-${timestamp}.csv`;
    const fullBlobPath = `${blobBasePath.replace(/\/$/, "")}/${fileName}`;

    const sharedKeyCredential = new StorageSharedKeyCredential(
      accountName,
      accountKey
    );
    const blobServiceClient = new BlobServiceClient(
      `https://${accountName}.blob.core.windows.net`,
      sharedKeyCredential
    );

    const containerClient = blobServiceClient.getContainerClient(containerName);

    const exists = await containerClient.exists();
    if (!exists) {
      strapi.log.info(
        `Container '${containerName}' does not exist. Creating it...`
      );
      await containerClient.create();
      strapi.log.info(`Container '${containerName}' created successfully.`);
    }

    const blockBlobClient = containerClient.getBlockBlobClient(fullBlobPath);

    const azureUploadPromise = blockBlobClient.uploadStream(
      passThrough,
      undefined,
      undefined,
      {
        blobHTTPHeaders: { blobContentType: "text/csv" },
      }
    );

    // Process data and write to the stream
    const batchSize = 1000;
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const products = await strapi.db.query("api::product.product").findMany({
        offset,
        limit: batchSize,
        populate: ["categories"],
      });

      if (!products.length) {
        hasMore = false;
        break;
      }

      for (const product of products) {
        const row = {
          code: product.product_id || "",
          creationSystem: product.creationSystem || "",
          name: product.name || "",
          description: product.description || "",
          productType: product.productType || "",
          itemsPerSalesUnit: "",
          color: product.color || "",
          size: product.size || "",
          style: product.style || "",
          capacity: product.capacity || "",
          livingGreen: product.livingGreen ?? "",
          threadCount: product.threadCount || "",
          fiberContent: product.fiberContent || "",
          fillType: product.fillType || "",
          fillWeight: product.fillWeight || "",
          manufacturerPartNumber: "",
          adaCompliant: product.adaCompliant ?? "",
          supercategories:
            product.categories?.map((cat) => cat.name).join(", ") || "",
          parentProduct: product.parentProduct || "",
          variantType: product.ah_variant_type || "",
          variantSort: product.variantSort || "",
          variantPlpName: product.variantPlpName || "",
          sapHierarchy1: product.sapHierarchy1 || "",
          sapHierarchy2: product.sapHierarchy2 || "",
          sapHierarchy3: product.sapHierarchy3 || "",
          sapHierarchy4: product.sapHierarchy4 || "",
          sapHierarchy5: product.sapHierarchy5 || "",
        };

        stream.write(row);
      }

      offset += batchSize;
    }

    // End the stream
    stream.end();

    // Wait for Azure upload to finish
    await azureUploadPromise;

    strapi.log.info(
      `Product export uploaded to Azure Blob Storage at '${fullBlobPath}'.`
    );
  } catch (err) {
    strapi.log.error("Error exporting/uploading Product:", err);
  }
};

const uploadProductSpecificationToAzure = async () => {
  try {
    const passThrough = new PassThrough();

    // Create the CSV stream
    const stream = stringify({
      header: true,
      delimiter: "|",
      columns: [
        "code",
        "adaCompliant",
        "hazmatCode",
        "sds",
        "livingGreen",
        "ulApproved",
        "culApproved",
        "prop65",
        "prop65Chemical",
        "prop65ChemType",
        "energyStar",
        "warranty",
        "warrantyType",
        "certification",
        "productWeight",
        "productLength",
        "productHeight",
        "productWidth",
        "shippingWeight",
        "shippingLength",
        "shippingHeight",
        "shippingWidth",
        "flavor",
        "coating",
        "shape",
        "applicableMaterial",
        "mount",
        "sealType",
        "cleaner",
        "productForm",
        "scent",
        "concentrated",
        "density",
        "gauge",
        "material",
        "closureType",
        "numberOfShelves",
        "doorStyle",
        "piecePerSet",
        "numberOfPlys",
        "sheetCount",
        "amperage",
        "hetz",
        "voltage",
        "wattage",
        "eel",
        "heat",
        "heatBTU",
        "coolBTU",
        "refrigerantType",
        "averageCoveringArea",
        "noiseLevel",
        "powerSource",
        "plugType",
        "outlet",
        "cordLength",
        "filter",
        "lbsPerDozen",
        "hemColor",
        "pocketDepth",
        "gsm",
        "hoseLength",
        "cleaningPath",
        "oekoTex",
        "fscCertified",
        "greenSealCertified",
        "diversitySupplier",
        "manufacturerName",
        "truckOnly",
        "pfas",
        "brandName",
        "diversityCertificate",
        "guage",
        "hertz",
        "refigerantType",
        "originCountry",
        "shippingUnit",
      ],
      quote: true,
    });

    // Pipe the CSV data to passThrough
    stream.pipe(passThrough);

    const accountName = process.env.AZURE_BLOB_ACCOUNT_NAME;
    const accountKey = process.env.AZURE_BLOB_ACCOUNT_KEY;
    const containerName = process.env.AZURE_BLOB_CONTAINER_NAME;
    const blobBasePath = process.env.AZURE_BLOB_FILE_PATH || "";

    // Generate timestamped filename
    const now = new Date();
    const timestamp = now
      .toISOString()
      .replace(/[-:.TZ]/g, "")
      .slice(0, 14);
    const fileName = `specifications-${timestamp}.csv`;
    const fullBlobPath = `${blobBasePath.replace(/\/$/, "")}/${fileName}`;

    const sharedKeyCredential = new StorageSharedKeyCredential(
      accountName,
      accountKey
    );
    const blobServiceClient = new BlobServiceClient(
      `https://${accountName}.blob.core.windows.net`,
      sharedKeyCredential
    );

    const containerClient = blobServiceClient.getContainerClient(containerName);

    const exists = await containerClient.exists();
    if (!exists) {
      strapi.log.info(
        `Container '${containerName}' does not exist. Creating it...`
      );
      await containerClient.create();
      strapi.log.info(`Container '${containerName}' created successfully.`);
    }

    const blockBlobClient = containerClient.getBlockBlobClient(fullBlobPath);

    const azureUploadPromise = blockBlobClient.uploadStream(
      passThrough,
      undefined,
      undefined,
      {
        blobHTTPHeaders: { blobContentType: "text/csv" },
      }
    );

    // Process data and write to the stream
    const batchSize = 1000;
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const products = await strapi.db.query("api::product.product").findMany({
        offset,
        limit: batchSize,
        populate: ["categories"],
      });

      if (!products.length) {
        hasMore = false;
        break;
      }

      for (const product of products) {
        const row = {
          code: product.product_id || "",
          adaCompliant: product.adaCompliant ?? "",
          hazmatCode: product.hazmatCode || "",
          sds: product.sds || "",
          livingGreen: product.livingGreen ?? "",
          ulApproved: product.ulApproved ?? "",
          culApproved: product.culApproved ?? "",
          prop65: product.prop65 ?? "",
          prop65Chemical: product.prop65Chemical || "",
          prop65ChemType: product.prop65ChemType || "",
          energyStar: product.energyStar ?? "",
          warranty: product.warranty || "",
          warrantyType: product.warrantyType || "",
          certification: product.certification || "",
          productWeight: product.productWeight || "",
          productLength: product.productLength || "",
          productHeight: product.productHeight || "",
          productWidth: product.productWidth || "",
          shippingWeight: product.shippingWeight || "",
          shippingLength: product.shippingLength || "",
          shippingHeight: product.shippingHeight || "",
          shippingWidth: product.shippingWidth || "",
          flavor: product.flavor || "",
          coating: product.coating || "",
          shape: product.shape || "",
          applicableMaterial: product.applicableMaterial || "",
          mount: product.mount || "",
          sealType: product.sealType || "",
          cleaner: product.cleaner || "",
          productForm: product.productForm || "",
          scent: product.scent || "",
          concentrated: product.concentrated ?? "",
          density: product.density || "",
          gauge: product.gauge || "",
          material: product.material || "",
          closureType: product.closureType || "",
          numberOfShelves: product.numberOfShelves || "",
          doorStyle: product.doorStyle || "",
          piecePerSet: product.piecePerSet || "",
          numberOfPlys: product.numberOfPlys || "",
          sheetCount: product.sheetCount || "",
          amperage: product.amperage || "",
          hetz: product.hetz || "",
          voltage: product.voltage || "",
          wattage: product.wattage || "",
          eel: product.eel || "",
          heat: product.heat ?? "",
          heatBTU: product.heatBTU || "",
          coolBTU: product.coolBTU || "",
          refrigerantType: product.refrigerantType || "",
          averageCoveringArea: product.averageCoveringArea || "",
          noiseLevel: product.noiseLevel || "",
          powerSource: product.powerSource || "",
          plugType: product.plugType || "",
          outlet: product.outlet || "",
          cordLength: product.cordLength || "",
          filter: product.filter || "",
          lbsPerDozen: product.lbsPerDozen || "",
          hemColor: product.hemColor || "",
          pocketDepth: product.pocketDepth || "",
          gsm: product.gsm || "",
          hoseLength: product.hoseLength || "",
          cleaningPath: product.cleaningPath || "",
          oekoTex: product.oekoTex ?? "",
          fscCertified: product.fscCertified ?? "",
          greenSealCertified: product.greenSealCertified ?? "",
          diversitySupplier: product.diversitySupplier ?? "",
          manufacturerName: product.manufacturerName ?? "",
          truckOnly: product.truckOnly ?? "",
          pfas: product.pfas ?? "",
          brandName: product.brandName ?? "",
          diversityCertificate: product.diversityCertificate ?? "",
          guage: product.guage ?? "",
          hertz: product.hertz ?? "",
          refigerantType: product.refigerantType ?? "",
          originCountry: product.originCountry ?? "",
          shippingUnit: product.shippingUnit ?? "",
        };

        stream.write(row);
      }

      offset += batchSize;
    }

    // End the stream
    stream.end();

    // Wait for Azure upload to finish
    await azureUploadPromise;

    strapi.log.info(
      `Product export uploaded to Azure Blob Storage at '${fullBlobPath}'.`
    );
  } catch (err) {
    strapi.log.error("Error exporting/uploading Product:", err);
  }
};

const uploadProductMediaToAzure = async () => {
  try {
    const passThrough = new PassThrough();

    // Create the CSV stream
    const stream = stringify({
      header: true,
      delimiter: "|",
      columns: ["code", "product", "order"],
      quote: true,
    });

    // Pipe the CSV data to passThrough
    stream.pipe(passThrough);

    const accountName = process.env.AZURE_BLOB_ACCOUNT_NAME;
    const accountKey = process.env.AZURE_BLOB_ACCOUNT_KEY;
    const containerName = process.env.AZURE_BLOB_CONTAINER_NAME;
    const blobBasePath = process.env.AZURE_BLOB_FILE_PATH || "";

    // Generate timestamped filename
    const now = new Date();
    const timestamp = now
      .toISOString()
      .replace(/[-:.TZ]/g, "")
      .slice(0, 14);
    const fileName = `product_media-${timestamp}.csv`;
    const fullBlobPath = `${blobBasePath.replace(/\/$/, "")}/${fileName}`;

    const sharedKeyCredential = new StorageSharedKeyCredential(
      accountName,
      accountKey
    );
    const blobServiceClient = new BlobServiceClient(
      `https://${accountName}.blob.core.windows.net`,
      sharedKeyCredential
    );

    const containerClient = blobServiceClient.getContainerClient(containerName);

    const exists = await containerClient.exists();
    if (!exists) {
      strapi.log.info(
        `Container '${containerName}' does not exist. Creating it...`
      );
      await containerClient.create();
      strapi.log.info(`Container '${containerName}' created successfully.`);
    }

    const blockBlobClient = containerClient.getBlockBlobClient(fullBlobPath);

    const azureUploadPromise = blockBlobClient.uploadStream(
      passThrough,
      undefined,
      undefined,
      {
        blobHTTPHeaders: { blobContentType: "text/csv" },
      }
    );

    // Process data and write to the stream
    const batchSize = 1000;
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const productMedias = await strapi.db
        .query("api::product-media.product-media")
        .findMany({
          offset,
          limit: batchSize,
        });

      if (!productMedias.length) {
        hasMore = false;
        break;
      }

      for (const productMedia of productMedias) {
        const row = {
          code: productMedia.file_path || "",
          product: productMedia.product_id || "",
          order: productMedia.code || "",
        };

        stream.write(row);
      }

      offset += batchSize;
    }

    // End the stream
    stream.end();

    // Wait for Azure upload to finish
    await azureUploadPromise;

    strapi.log.info(
      `Product Media export uploaded to Azure Blob Storage at '${fullBlobPath}'.`
    );
  } catch (err) {
    strapi.log.error("Error exporting/uploading Product Media:", err);
  }
};

export {
  chunkArray,
  syncBulkCustomerInternal,
  fetchProductHierarchy,
  syncBulkProductInternal,
  uploadFGControlMainTextToAzure,
  uploadProductWebAttributeToAzure,
  uploadProductSpecificationToAzure,
  uploadProductMediaToAzure,
};
