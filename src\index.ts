import { Core } from "@strapi/strapi";

export default {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  async register(/* { strapi }: { strapi: Core.Strapi } */) { },

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  bootstrap({ strapi }: { strapi: Core.Strapi }) {
    strapi.db.lifecycles.subscribe({
      models: ['plugin::upload.file'],
      async afterCreate(event) {
        const getFileType = (mimeType) => {
          if (mimeType.startsWith("image/")) return "IMAGE";
          if (mimeType.startsWith("video/")) return "VIDEO";
          if (mimeType === "application/pdf") return "PDF";
          return "";
        }
        const { result } = event;
        if (result.name) {
          const fileType = getFileType(result.mime);
          const nameWithoutExtension = result.name.split('.')[0];
          const [first, second, third] = nameWithoutExtension.split('_');
          if (fileType) {
            if (first.toLowerCase() == 'cat') {
              await strapi
                .query(
                  "api::product-category.product-category"
                )
                .update({
                  where: { category_id: second },
                  data: {
                    url: result.url,
                  },
                });
            } else {
              const product = await strapi.query(`api::product.product`).findOne({
                where: {
                  product_id: first
                },
              });
              if (product) {
                let filePath = nameWithoutExtension;
                // Extract folder IDs from folderPath and get folder names
                if (result.folderPath && result.folderPath !== '/') {
                  try {
                    // Split folderPath to get all folder IDs (e.g., "/1/2/3" -> ["1", "2", "3"])
                    const folderIds = result.folderPath.split('/').filter((id: string) => id !== '');

                    // Fetch only the needed folders based on folderIds
                    const neededFolders = await strapi.query('plugin::upload.folder').findMany({
                      where: {
                        pathId: {
                          $in: folderIds.map((id: string) => parseInt(id))
                        }
                      },
                      select: ['id', 'name', 'pathId']
                    });

                    // Create a map for quick folder lookup by ID
                    const folderMap = new Map();
                    neededFolders.forEach((folder: any) => {
                      folderMap.set(folder.pathId, folder);
                    });

                    // Build the folder path using the folder IDs from folderPath
                    const folderNames = [];
                    for (const folderId of folderIds) {
                      const folder = folderMap.get(parseInt(folderId));
                      if (folder) {
                        folderNames.push(folder.name);
                      }
                    }

                    const folderPath = folderNames.join('/');
                    filePath = folderPath ? `${folderPath}/${nameWithoutExtension}` : nameWithoutExtension;

                  } catch (error) {
                    console.error("Error fetching folder details:", error);
                  }
                }

                await strapi.documents(`api::product-media.product-media`).create({
                  data: {
                    product_id: first,
                    file_path: filePath,
                    code: (second || '').toString(),
                    url: result.url,
                    media_type: fileType,
                    is_cover_image: second == 1 ? true : false
                  },
                });
              }
            }
          }
        }
      },
    });
  },
};
