const chunkArray = (array, size) => {
  return Array.from({ length: Math.ceil(array.length / size) }, (_, index) =>
    array.slice(index * size, index * size + size)
  );
};

const getHierarchy = async (node) => {
  const hierarchy = await strapi.entityService.findMany(
    "api::product-hierarchy.product-hierarchy",
    {
      filters: {
        $or: [{ parent_node: node }, { child_node: node }],
      },
      populate: {
        children: {
          populate: "children",
        },
      },
    }
  );

  return hierarchy;
};

const extractProductIds = (node, ids = []) => {
  ids.push(node.product_id);

  if (node.children && node.children.length > 0) {
    node.children.forEach((child) => {
      extractProductIds(child, ids);
    });
  }

  return ids;
};

const fetchProductHierarchy = async (node) => {
  const rootNode = await getHierarchy(node);

  if (!rootNode || rootNode.length === 0) {
    return [];
  }

  let allProductIds = [];
  rootNode.forEach((node) => {
    allProductIds = allProductIds.concat(extractProductIds(node));
  });

  return allProductIds;
};

const syncBulkProductInternal = async (dataBatch) => {
  try {
    if (!dataBatch || dataBatch.length === 0) return;

    const flexGroupIds = dataBatch.map((d) => d.flex_group_id);
    const productIds = dataBatch.map((d) => d.product_id);
    const locales = dataBatch.map((d) => d.locale || "en");

    // Step 1: Find existing records
    const existingRecords = await strapi.db
      .query("api::fg-product-internal.fg-product-internal")
      .findMany({
        where: {
          flex_group_id: { $in: flexGroupIds },
          product_id: { $in: productIds },
          locale: { $in: locales },
        },
        select: ["flex_group_id", "product_id", "locale"],
      });

    // Step 2: Convert existing records into a Set for quick lookup
    const existingSet = new Set(
      existingRecords.map(
        (r) => `${r.flex_group_id}-${r.product_id}-${r.locale}`
      )
    );

    // Step 3: Filter out duplicates before inserting
    const bulkInsertData = dataBatch.filter(
      (d) => !existingSet.has(`${d.flex_group_id}-${d.product_id}-${d.locale}`)
    );

    if (bulkInsertData.length > 0) {
      await strapi.db
        .query("api::fg-product-internal.fg-product-internal")
        .createMany({
          data: bulkInsertData,
        });
    }
  } catch (error) {
    console.error("Bulk insert error in syncBulkProductInternal:", error);
  }
};

export {
  chunkArray,
  fetchProductHierarchy,
  syncBulkProductInternal,
};
