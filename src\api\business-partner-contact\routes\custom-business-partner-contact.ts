/**
 * configuration custom router
 */

export default {
  routes: [
    {
      method: "POST",
      path: "/business-partner-contact/registration",
      handler: "business-partner-contact.registration",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "POST",
      path: "/business-partner-contact/mapping",
      handler: "business-partner-contact.bpContactMapping",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "PUT",
      path: "/business-partner-contact/:documentId/save",
      handler: "business-partner-contact.save",
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
