{"kind": "collectionType", "collectionName": "crm_organisational_unit_functions", "info": {"singularName": "crm-organisational-unit-function", "pluralName": "crm-organisational-unit-functions", "displayName": "CRM Organisational Unit Function"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"end_date": {"type": "date"}, "start_date": {"type": "date"}, "service_organisation_indicator": {"type": "boolean"}, "service_indicator": {"type": "boolean"}, "sales_organisation_indicator": {"type": "boolean"}, "sales_office_indicator": {"type": "boolean"}, "sales_indicator": {"type": "boolean"}, "sales_group_indicator": {"type": "boolean"}, "reporting_line_indicator": {"type": "boolean"}, "currency_code": {"type": "string"}, "company_indicator": {"type": "boolean"}, "marketing_indicator": {"type": "boolean"}, "organisational_unit_id": {"type": "string"}, "organisational_unit": {"type": "relation", "relation": "manyToOne", "target": "api::crm-organisational-unit.crm-organisational-unit", "inversedBy": "crm_org_unit_functions"}}}