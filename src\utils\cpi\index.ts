import axios, { AxiosInstance, AxiosRequestConfig } from "axios";
import qs from "qs";

let accessToken: string | null = null;
let tokenExpiresAt: number | null = null;

const baseUrl = process.env.SAP_CPI_BASE_URL!;
const tokenUrl = process.env.SAP_CPI_TOKEN_URL!;
const clientId = process.env.SAP_CPI_CLIENT_ID!;
const clientSecret = process.env.SAP_CPI_CLIENT_SECRET!;

let axiosInstance: AxiosInstance | null = null;

const getAccessToken = async (): Promise<string> => {
  if (
    accessToken &&
    tokenExpiresAt &&
    Date.now() < tokenExpiresAt - 60000 // valid for >1min
  ) {
    strapi.log.debug("Using cached access token");
    return accessToken;
  }

  const auth = Buffer.from(`${clientId}:${clientSecret}`).toString("base64");
  const data = qs.stringify({ grant_type: "client_credentials" });

  try {
    const res = await axios.post(tokenUrl, data, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${auth}`,
      },
    });

    accessToken = res.data.access_token;
    tokenExpiresAt = Date.now() + res.data.expires_in * 1000;

    return accessToken!;
  } catch (err) {
    strapi.log.error("Error fetching CPI access token", err);
    throw err;
  }
};

const setupAxiosInstance = async (): Promise<AxiosInstance> => {
  if (axiosInstance) return axiosInstance;

  const instance = axios.create({ baseURL: baseUrl });

  // Request Interceptor
  instance.interceptors.request.use(
    async (config: AxiosRequestConfig): Promise<any> => {
      const token = await getAccessToken();
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response Interceptor for 401 retry
  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;

      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        accessToken = null;
        const token = await getAccessToken();
        originalRequest.headers["Authorization"] = `Bearer ${token}`;
        return instance(originalRequest);
      }

      return Promise.reject(error);
    }
  );

  axiosInstance = instance;
  return instance;
};

export const getCpiAxios = async (): Promise<AxiosInstance> => {
  return await setupAxiosInstance();
};

export const generateUniqueID = async (sequenceName: string) => {
  // Get the next value from PostgreSQL sequence
  const q: string = `SELECT nextval('${sequenceName}')`;
  const result = await strapi.db.connection.raw(q);
  const nextNumber = result.rows[0].nextval;

  // pad with zeros
  return `${String(nextNumber).padStart(9, "0")}`;
};

export const ReplicateBPToHybris = async (
  body: Record<string, any>
): Promise<ReplicateBusinessPartnerResult | null> => {
  try {
    const cpiAxios = await getCpiAxios();
    const resp = await cpiAxios.post(
      process.env.SAP_CPI_REPLICATE_BP_HYBRIS!,
      body
    );

    const results = resp?.data?.RESULTS;
    const info = results?.INFO;
    const status = info?.STATUS;
    const message = info?.MESSAGE;
    const errorCode = info?.CODE || 400;

    if (status === "Failed") {
      strapi.log.warn(`CPI responded with failure: ${message}`);
      throw new Error(`CPI Sync Failed (${errorCode}): ${message}`);
    }

    strapi.log.info(
      `Replicate business partner successfully synced with S4HANA`
    );
    return results;
  } catch (error) {
    strapi.log.error(`Failed to replicate business partner: ${error.message}`);
    return null; // or throw error if you want to handle it at a higher level
  }
};

interface ReplicateBusinessPartnerResult {
  INFO: {
    STATUS: string;
    MESSAGE: string;
    CODE: number;
  };
  [key: string]: any;
}

export const ReplicateBusinessPartner = async (
  body: Record<string, any>
): Promise<ReplicateBusinessPartnerResult | null> => {
  try {
    const cpiAxios = await getCpiAxios();
    const resp = await cpiAxios.post(
      process.env.SAP_CPI_REPLICATE_BUSINESS_PARTNER!,
      body
    );

    const results = resp?.data?.RESULTS;
    const info = results?.INFO;
    const status = info?.STATUS;
    const message = info?.MESSAGE;
    const errorCode = info?.CODE || 400;

    if (status === "Failed") {
      strapi.log.warn(`CPI responded with failure: ${message}`);
      throw new Error(`CPI Sync Failed (${errorCode}): ${message}`);
    }

    strapi.log.info(
      `Replicate business partner successfully synced with S4HANA`
    );
    return results;
  } catch (error) {
    strapi.log.error(`Failed to replicate business partner: ${error.message}`);
    return null; // or throw error if you want to handle it at a higher level
  }
};

export const ReplicateBusinessPartnerContact = async (bp_id): Promise<any> => {
  try {
    const bp = await strapi
      .query("api::business-partner.business-partner")
      .findOne({
        where: {
          bp_id,
          roles: {
            bp_role: {
              $ne: "PRO001", // not equal to 'PRO001' (Prospect)
            },
          },
        },
        populate: {
          contact_companies: {
            populate: {
              person_func_and_dept: true,
              business_partner_person: {
                populate: {
                  addresses: {
                    populate: {
                      emails: true,
                      fax_numbers: true,
                      home_page_urls: true,
                      phone_numbers: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

    if (bp) {
      const payload = BusinessPartnerContactPayload(bp);
      strapi.log.info("ReplicateBusinessPartnerContactPayload", payload);
      await ReplicateBusinessPartner(payload);
    }
  } catch (error) {
    strapi.log.error(
      `Failed to replicate business partner contact: ${error.message}`
    );
  }
};

const BusinessPartnerContactPayload = (data: any): any => {
  const formatAddress = (address: any) => ({
    Country: address.country_code || "US",
    Region: address.region || "",
    HouseNumber: address.house_number || "",
    AdditionalStreetPrefixName: address.additional_street_prefix_name || "",
    AdditionalStreetSuffixName: address.additional_street_suffix_name || "",
    StreetName: address.street_name || "",
    PostalCode: address.postal_code || "",
    CityName: address.city_name || "",
    PrfrdCommMediumType: address.prfrd_comm_medium_type || "",
    Language: "EN",
    to_AddressUsage:
      address.address_usages?.map((usage: any) => ({
        AddressUsage: usage.address_usage || "XXDEFAULT",
      })) || [],
    to_EmailAddress:
      address.emails?.map((email: any) => ({
        EmailAddress: email.email_address || "",
      })) || [],
    to_PhoneNumber:
      address.phone_numbers
        ?.filter((ph: any) => ph.phone_number_type === "1")
        .map((ph: any) => ({ PhoneNumber: ph.phone_number || "" })) || [],
    to_URLAddress:
      address.home_page_urls?.map((url: any) => ({
        WebsiteURL: url.website_url || "",
      })) || [],
    to_FaxNumber:
      address.fax_numbers?.map((fax: any) => ({
        FaxNumber: fax.fax_number || "",
      })) || [],
    to_MobilePhoneNumber:
      address.phone_numbers
        ?.filter((ph: any) => ph.phone_number_type === "3")
        .map((ph: any) => ({ PhoneNumber: ph.phone_number || "" })) || [],
  });

  const contacts = (data.contact_companies || []).map((company: any) => ({
    BusinessPartner: company.bp_person_id || "",
    Name1: company.business_partner_person?.first_name || "",
    Name2: company.business_partner_person?.middle_name || "",
    Name3: company.business_partner_person?.last_name || "",
    CategoryCode: "1",
    IsMarkedForArchiving:
      company?.business_partner_person?.is_marked_for_archiving || false,
    to_BusinessPartnerAddress:
      company.business_partner_person?.addresses?.map(formatAddress) || [],
    to_BusinessPartnerRole: [{ BusinessPartnerRole: "BUP001" }],
  }));

  return {
    BP: [...contacts],
    BP_Relationship: (data?.contact_companies || []).map((company: any) => ({
      BusinessPartnerID: company?.bp_company_id || "",
      RelationshipBusinessPartnerID: company?.bp_person_id || "",
      ValidityStartDate: company?.validity_start_date || new Date(),
      ValidityEndDate:
        company?.validity_end_date || new Date("9999-12-29T23:59:59.000Z"),
      RoleCode: "BUR001",
      ContactPerson: {
        ContactPersonVipType: company?.person_func_and_dept
          ?.contact_person_vip_type
          ? "1"
          : "",
        ContactPersonDepartment:
          company?.person_func_and_dept?.contact_person_department || "",
        ContactPersonFunction:
          company?.person_func_and_dept?.contact_person_function || "",
      },
    })),
  };
};
