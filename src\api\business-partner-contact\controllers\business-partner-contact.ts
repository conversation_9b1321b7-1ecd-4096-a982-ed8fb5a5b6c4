/**
 * business-partner-contact controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";
import {
  generateUniqueID,
  ReplicateBPToHybris,
  ReplicateBusinessPartnerContact,
} from "../../../utils/cpi";

export default factories.createCoreController(
  "api::business-partner-contact.business-partner-contact",
  ({ strapi }) => ({
    async registration(ctx: Context) {
      try {
        const {
          first_name,
          middle_name,
          last_name,
          contact_person_department,
          contact_person_department_name,
          contact_person_function,
          contact_person_function_name,
          contact_person_vip_type,
          job_title,
          business_department,
          best_reached_by,
          web_registered,
          emails_opt_in,
          print_marketing_opt_in,
          sms_promotions_opt_in,
          prfrd_comm_medium_type,
          purchasing_control,
          native_language,
          bp_id,
        } = ctx.request.body;

        if (!bp_id) {
          return ctx.throw(400, "bp_id required");
        }

        const locale = "en";

        const newBP = await strapi.db.transaction(async () => {
          const contact_bp_id = `S${await generateUniqueID("bp_contact_id_seq")}`;
          let data: any = {
            bp_id: contact_bp_id,
            bp_full_name: [first_name, last_name]
              .filter(Boolean)
              .join(" ")
              .trim(),
            first_name,
            middle_name,
            last_name,
            locale,
          };

          // Create Business Partner Person
          const bp_person = await strapi
            .query("api::business-partner.business-partner")
            .create({ data });

          // Find BP extension
          const bpExtension = await strapi.db
            .query("api::business-partner-extension.business-partner-extension")
            .findOne({
              where: { bp_id: bp_person.bp_id },
            });

          if (bpExtension) {
            // **Update BP extension**
            await strapi
              .query(
                "api::business-partner-extension.business-partner-extension"
              )
              .update({
                where: { id: bpExtension.id },
                data: {
                  job_title,
                  business_department,
                  best_reached_by,
                  web_registered,
                  emails_opt_in,
                  print_marketing_opt_in,
                  sms_promotions_opt_in,
                  purchasing_control,
                  native_language,
                },
              });
          } else {
            // **Create BP extension if not exists**
            await strapi
              .query(
                "api::business-partner-extension.business-partner-extension"
              )
              .create({
                data: {
                  bp_id: bp_person.bp_id,
                  job_title,
                  business_department,
                  best_reached_by,
                  web_registered,
                  emails_opt_in,
                  print_marketing_opt_in,
                  sms_promotions_opt_in,
                  purchasing_control,
                  native_language,
                  locale: bp_person.locale,
                },
              });
          }

          // Assign a BP Role
          data = {
            bp_role: "BUP001",
            bp_id: bp_person.bp_id,
            locale,
          };

          await strapi
            .query("api::business-partner-role.business-partner-role")
            .create({ data });

          // **Find or Create Business Partner Relationship**
          let bp_relationship = await strapi
            .query(
              "api::business-partner-relationship.business-partner-relationship"
            )
            .findOne({
              where: {
                bp_id1: bp_id,
                bp_id2: bp_person.bp_id,
              },
            });

          if (!bp_relationship) {
            data = {
              relationship_number: `REL${Date.now()}`, // Generate a unique relationship number
              bp_id1: bp_id, // Company
              bp_id2: bp_person.bp_id, // Contact
              validity_start_date: new Date(),
              validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
              locale,
            };

            bp_relationship = await strapi
              .query(
                "api::business-partner-relationship.business-partner-relationship"
              )
              .create({ data });
          }

          // Create BP Contact linked to Relationship
          data = {
            bp_person_id: bp_person.bp_id,
            bp_company_id: bp_id,
            relationship_number: bp_relationship.relationship_number,
            relationship: bp_relationship.id, // Link to relationship
            validity_start_date: new Date(),
            validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
            locale,
          };

          const bpContact = await strapi
            .query("api::business-partner-contact.business-partner-contact")
            .create({ data });

          // **Insert Department & Function**
          if (
            contact_person_department ||
            contact_person_function ||
            contact_person_vip_type
          ) {
            data = {
              bp_person_id: bp_person.bp_id,
              bp_company_id: bp_id,
              relationship_number: bp_relationship.relationship_number,
              relationship: bp_relationship.id, // Link to relationship
              validity_start_date: new Date(),
              validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
              locale,
            };

            if (contact_person_department) {
              data.contact_person_department = contact_person_department;
              data.contact_person_department_name =
                contact_person_department_name;
            }

            if (contact_person_function) {
              data.contact_person_function = contact_person_function;
              data.contact_person_function_name = contact_person_function_name;
            }

            // If VIP is true, reset others first
            if (contact_person_vip_type === true) {
              await strapi.db
                .query(
                  "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
                )
                .updateMany({
                  where: {
                    bp_company_id: bp_id,
                    contact_person_vip_type: true,
                  },
                  data: { contact_person_vip_type: false },
                });
            }

            if (typeof contact_person_vip_type === "boolean") {
              data.contact_person_vip_type = contact_person_vip_type;
            }

            await strapi
              .query(
                "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
              )
              .create({ data });
          }

          // Find Default Address
          const address = await strapi.db
            .query("api::business-partner-address.business-partner-address")
            .findOne({
              where: {
                bp_id,
                address_usages: {
                  address_usage: "XXDEFAULT",
                },
              },
            });

          // Create Contact Address linked to Relationship
          data = {
            house_number: address?.house_number,
            additional_street_prefix_name:
              address?.additional_street_prefix_name,
            additional_street_suffix_name:
              address?.additional_street_suffix_name,
            street_name: address?.street_name,
            city_name: address?.city_name,
            country: address?.country,
            county_code: address?.county_code,
            postal_code: address?.postal_code,
            region: address?.region,
            validity_start_date: new Date(),
            validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
            bp_id: bp_person.bp_id,
            prfrd_comm_medium_type,
            locale,
          };

          const contact_address = await strapi
            .query("api::business-partner-address.business-partner-address")
            .create({ data });

          // Create Contact Info (Email, Phone, Fax, Website) linked to Relationship
          const contactInfoTypes = [
            {
              key: "email_address",
              table: "bp-email-address.bp-email-address",
            },
            { key: "fax_number", table: "bp-fax-number.bp-fax-number" },
            { key: "website_url", table: "bp-home-page-url.bp-home-page-url" },
            { key: "phone_number", table: "bp-phone-number.bp-phone-number" },
            { key: "mobile", table: "bp-phone-number.bp-phone-number" },
          ];

          for (const info of contactInfoTypes) {
            if (ctx.request.body[info.key]) {
              if (["phone_number", "mobile"].includes(info.key)) {
                data = {
                  phone_number: ctx.request.body[info.key],
                  phone_number_type: info.key === "phone_number" ? "1" : "3",
                  business_partner_address: {
                    connect: [contact_address.id],
                  },
                  locale,
                };
                const t: any = `api::${info.table}`;
                await strapi.query(t).create({ data });
              } else if (!["phone_number", "mobile"].includes(info.key)) {
                data = {
                  [info.key]: ctx.request.body[info.key],
                  business_partner_address: {
                    connect: [contact_address.id],
                  },
                  locale,
                };
                const t: any = `api::${info.table}`;
                await strapi.query(t).create({ data });
              }
            }
          }

          setImmediate(async () => {
            await ReplicateBusinessPartnerContact(bp_id);
          });

          return bpContact;
        });

        return ctx.send({
          message: "Business partner contact registered successfully",
          data: newBP,
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to register Business partner contact: ${error.message}`
        );
      }
    },
    async save(ctx: Context) {
      try {
        const { documentId } = ctx.params;
        const {
          first_name,
          middle_name,
          last_name,
          email_address,
          fax_number,
          website_url,
          phone_number,
          mobile,
          contact_person_department,
          contact_person_department_name,
          contact_person_function,
          contact_person_function_name,
          contact_person_vip_type,
          job_title,
          business_department,
          best_reached_by,
          web_registered,
          emails_opt_in,
          print_marketing_opt_in,
          sms_promotions_opt_in,
          prfrd_comm_medium_type,
          purchasing_control,
          native_language,
          validity_end_date,
          is_marked_for_archiving,
        } = ctx.request.body;

        if (!documentId) {
          return ctx.throw(
            400,
            "Business Partner document id is missing in URL param"
          );
        }

        const updateBP = await strapi.db.transaction(async () => {
          // Find BP Contact
          const bpContact = await strapi.db
            .query("api::business-partner-contact.business-partner-contact")
            .findOne({
              where: { documentId },
              populate: {
                business_partner_person: true,
                business_partner_company: true,
              },
            });

          if (!bpContact) {
            return ctx.throw(404, "Business Partner Contact not found");
          }

          if (bpContact && validity_end_date) {
            await strapi
              .query("api::business-partner-contact.business-partner-contact")
              .update({
                where: { id: bpContact?.id },
                data: {
                  validity_end_date: new Date(validity_end_date),
                },
              });
          }

          // Update Business Partner Person
          const bp_person = await strapi
            .query("api::business-partner.business-partner")
            .update({
              where: { id: bpContact?.business_partner_person?.id },
              data: {
                bp_full_name: [first_name, last_name]
                  .filter(Boolean)
                  .join(" ")
                  .trim(),
                first_name,
                middle_name,
                last_name,
                is_marked_for_archiving,
              },
            });

          // Find BP extension
          const bpExtension = await strapi.db
            .query("api::business-partner-extension.business-partner-extension")
            .findOne({
              where: { bp_id: bpContact.bp_person_id },
            });

          if (bpExtension) {
            // **Update BP extension**
            await strapi
              .query(
                "api::business-partner-extension.business-partner-extension"
              )
              .update({
                where: { id: bpExtension.id },
                data: {
                  job_title,
                  business_department,
                  best_reached_by,
                  web_registered,
                  emails_opt_in,
                  print_marketing_opt_in,
                  sms_promotions_opt_in,
                  purchasing_control,
                  native_language,
                },
              });
          } else {
            // **Create BP extension if not exists**
            await strapi
              .query(
                "api::business-partner-extension.business-partner-extension"
              )
              .create({
                data: {
                  bp_id: bpContact.bp_person_id,
                  job_title,
                  business_department,
                  best_reached_by,
                  web_registered,
                  emails_opt_in,
                  print_marketing_opt_in,
                  sms_promotions_opt_in,
                  purchasing_control,
                  native_language,
                  locale: bpContact.locale,
                },
              });
          }

          // Find BP Contact Address
          let address = await strapi.db
            .query("api::business-partner-address.business-partner-address")
            .findOne({
              where: {
                bp_id: bp_person.bp_id,
              },
            });

          if (!address) {
            // **Find or Create Business Partner Relationship**
            let bp_relationship = await strapi
              .query(
                "api::business-partner-relationship.business-partner-relationship"
              )
              .findOne({
                where: {
                  bp_id1: bpContact?.bp_company_id,
                  bp_id2: bpContact?.bp_person_id,
                },
              });

            if (!bp_relationship) {
              bp_relationship = await strapi
                .query(
                  "api::business-partner-relationship.business-partner-relationship"
                )
                .create({
                  data: {
                    relationship_number: `REL${Date.now()}`, // Generate a unique relationship number
                    bp_id1: bpContact?.bp_company_id, // Company
                    bp_id2: bpContact?.bp_person_id, // Contact
                    validity_start_date: new Date(),
                    validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
                    locale: bpContact?.locale,
                  },
                });
            }
            // Find Default Address
            const bpAddress = await strapi.db
              .query("api::business-partner-address.business-partner-address")
              .findOne({
                where: {
                  bp_id: bpContact?.bp_company_id,
                  address_usages: {
                    address_usage: "XXDEFAULT",
                  },
                },
              });

            address = await strapi
              .query("api::business-partner-address.business-partner-address")
              .create({
                data: {
                  house_number: bpAddress?.house_number,
                  additional_street_prefix_name:
                    bpAddress?.additional_street_prefix_name,
                  additional_street_suffix_name:
                    bpAddress?.additional_street_suffix_name,
                  street_name: bpAddress?.street_name,
                  city_name: bpAddress?.city_name,
                  country: bpAddress?.country,
                  county_code: bpAddress?.county_code,
                  postal_code: bpAddress?.postal_code,
                  region: bpAddress?.region,
                  validity_start_date: new Date(),
                  validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
                  bp_id: bp_person.bp_id,
                  prfrd_comm_medium_type,
                  locale: bpAddress?.locale,
                },
              });
          }

          if (address) {
            // Update Address
            await strapi
              .query("api::business-partner-address.business-partner-address")
              .update({
                where: { id: address.id },
                data: { prfrd_comm_medium_type },
              });

            // Email Address
            const email = await strapi
              .query("api::bp-email-address.bp-email-address")
              .findOne({
                where: {
                  business_partner_address: address.id,
                },
              });

            if (email) {
              await strapi
                .query("api::bp-email-address.bp-email-address")
                .update({
                  where: { id: email.id },
                  data: { email_address },
                });
            } else {
              await strapi
                .query("api::bp-email-address.bp-email-address")
                .create({
                  data: {
                    email_address,
                    business_partner_address: {
                      connect: [address.id],
                    },
                    locale: address.locale,
                  },
                });
            }

            // Fax Number
            const fax = await strapi
              .query("api::bp-fax-number.bp-fax-number")
              .findOne({
                where: {
                  business_partner_address: address.id,
                },
              });

            if (fax) {
              await strapi.query("api::bp-fax-number.bp-fax-number").update({
                where: { id: fax.id },
                data: { fax_number },
              });
            } else {
              await strapi.query("api::bp-fax-number.bp-fax-number").create({
                data: {
                  fax_number,
                  business_partner_address: {
                    connect: [address.id],
                  },
                  locale: address.locale,
                },
              });
            }

            // Website URL
            const website = await strapi
              .query("api::bp-home-page-url.bp-home-page-url")
              .findOne({
                where: {
                  business_partner_address: address.id,
                },
              });

            if (website) {
              await strapi
                .query("api::bp-home-page-url.bp-home-page-url")
                .update({
                  where: { id: website.id },
                  data: { website_url },
                });
            } else {
              await strapi
                .query("api::bp-home-page-url.bp-home-page-url")
                .create({
                  data: {
                    website_url,
                    business_partner_address: {
                      connect: [address.id],
                    },
                    locale: address.locale,
                  },
                });
            }

            const phone = await strapi
              .query("api::bp-phone-number.bp-phone-number")
              .findOne({
                where: {
                  business_partner_address: address.id,
                  phone_number_type: "1",
                },
              });

            if (phone) {
              await strapi
                .query("api::bp-phone-number.bp-phone-number")
                .update({
                  where: { id: phone.id },
                  data: { phone_number },
                });
            } else {
              await strapi
                .query("api::bp-phone-number.bp-phone-number")
                .create({
                  data: {
                    phone_number: phone_number,
                    phone_number_type: "1",
                    business_partner_address: {
                      connect: [address.id],
                    },
                    locale: address.locale,
                  },
                });
            }

            const mobileData = await strapi
              .query("api::bp-phone-number.bp-phone-number")
              .findOne({
                where: {
                  business_partner_address: address.id,
                  phone_number_type: "3",
                },
              });

            if (mobileData) {
              await strapi
                .query("api::bp-phone-number.bp-phone-number")
                .update({
                  where: { id: mobileData.id },
                  data: { phone_number: mobile },
                });
            } else {
              await strapi
                .query("api::bp-phone-number.bp-phone-number")
                .create({
                  data: {
                    phone_number: mobile,
                    phone_number_type: "3",
                    business_partner_address: {
                      connect: [address.id],
                    },
                    locale: address.locale,
                  },
                });
            }
          }

          // **Find or Update Department & Function**
          const bpContactFuncDept = await strapi.db
            .query(
              "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
            )
            .findOne({
              where: {
                bp_person_id: bpContact.bp_person_id,
                bp_company_id: bpContact.bp_company_id,
              },
            });

          if (contact_person_vip_type === true) {
            // Ensure only one VIP per relationship
            await strapi.db
              .query(
                "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
              )
              .updateMany({
                where: {
                  bp_company_id: bpContact.bp_company_id,
                  contact_person_vip_type: true,
                },
                data: { contact_person_vip_type: false },
              });
          }

          if (bpContactFuncDept) {
            // **Update Department & Function**
            await strapi
              .query(
                "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
              )
              .update({
                where: { id: bpContactFuncDept.id },
                data: {
                  contact_person_department: contact_person_department,
                  contact_person_department_name:
                    contact_person_department_name,
                  contact_person_function: contact_person_function,
                  contact_person_function_name: contact_person_function_name,
                  ...(typeof contact_person_vip_type === "boolean" && {
                    contact_person_vip_type,
                  }),
                },
              });
          } else {
            // **Find or Create Business Partner Relationship**
            let bp_relationship = await strapi
              .query(
                "api::business-partner-relationship.business-partner-relationship"
              )
              .findOne({
                where: {
                  bp_id1: bpContact.bp_company_id,
                  bp_id2: bp_person.bp_id,
                },
              });

            if (!bp_relationship) {
              const data = {
                relationship_number: `REL${Date.now()}`, // Generate a unique relationship number
                bp_id1: bpContact.bp_company_id, // Company
                bp_id2: bp_person.bp_id, // Contact
                validity_start_date: new Date(),
                validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
                locale: "en",
              };

              bp_relationship = await strapi
                .query(
                  "api::business-partner-relationship.business-partner-relationship"
                )
                .create({ data });
            }

            // **Create Department & Function if not exists**
            await strapi
              .query(
                "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
              )
              .create({
                data: {
                  relationship_number: bp_relationship.relationship_number,
                  relationship: bp_relationship.id, // Link to relationship
                  bp_person_id: bpContact.bp_person_id,
                  bp_company_id: bpContact.bp_company_id,
                  contact_person_department: contact_person_department,
                  contact_person_department_name:
                    contact_person_department_name,
                  contact_person_function: contact_person_function,
                  contact_person_function_name: contact_person_function_name,
                  ...(typeof contact_person_vip_type === "boolean" && {
                    contact_person_vip_type,
                  }),
                  validity_start_date: new Date(),
                  validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
                  locale: "en",
                },
              });
          }

          // Replication data with S4HANA and Hybris
          setImmediate(async () => {
            await ReplicateBusinessPartnerContact(bpContact.bp_company_id);
            if (
              bpExtension.emails_opt_in !== emails_opt_in &&
              bpExtension?.web_user_id
            ) {
              const body: any = {
                bp_id: bpContact.bp_company_id,
                web_registered,
                emails_opt_in,
                web_user_id: bpExtension?.web_user_id,
              };
              await ReplicateBPToHybris(body);
            }
          });

          return bp_person;
        });

        return ctx.send({
          message: "Business partner contact saved successfully",
          data: updateBP,
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to save Business Partner Contact: ${error.message}`
        );
      }
    },
    async bpContactMapping(ctx: Context) {
      try {
        const { bp_person_id, bp_id } = ctx.request.body;

        if (!bp_id && !bp_person_id) {
          return ctx.throw(400, "bp_id and bp_person_id are required");
        }

        const locale = "en";

        const newBP = await strapi.db.transaction(async () => {
          // **Find or Create Business Partner Relationship**
          let bp_relationship = await strapi
            .query(
              "api::business-partner-relationship.business-partner-relationship"
            )
            .findOne({
              where: {
                bp_id1: bp_id,
                bp_id2: bp_person_id,
              },
            });

          if (!bp_relationship) {
            bp_relationship = await strapi
              .query(
                "api::business-partner-relationship.business-partner-relationship"
              )
              .create({
                data: {
                  relationship_number: `REL${Date.now()}`, // Generate a unique relationship number
                  bp_id1: bp_id, // Company
                  bp_id2: bp_person_id, // Contact
                  validity_start_date: new Date(),
                  validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
                  relationship_category: "CONTACT",
                  bp_relationship_type: "CONTACT",
                  locale,
                },
              });
          }

          // Create BP Contact linked to Relationship
          const bpContact = await strapi
            .query("api::business-partner-contact.business-partner-contact")
            .create({
              data: {
                bp_person_id: bp_person_id,
                bp_company_id: bp_id,
                relationship_number: bp_relationship.relationship_number,
                relationship: bp_relationship.id, // Link to relationship
                validity_start_date: new Date(),
                validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
                locale,
              },
            });

          setImmediate(async () => {
            await ReplicateBusinessPartnerContact(bp_id);
          });

          return bpContact;
        });

        return ctx.send({
          message: "Business partner contact mapping successfully",
          data: newBP,
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to mapping Business partner contact: ${error.message}`
        );
      }
    },
  })
);
