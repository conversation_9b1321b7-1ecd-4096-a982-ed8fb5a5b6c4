import {
  BPAddrDepdntIntlLocNumber,
  BusinessPartner,
  BusinessPartnerAddress,
  BusinessPartnerAddressUsage,
  BusinessPartnerBank,
  BusinessPartnerContact,
  BusinessPartnerContactToAddress,
  BusinessPartnerContactToFuncAndDepts,
  BusinessPartnerCreditWorthiness,
  BusinessPartnerIdentification,
  BusinessPartnerInternationalAddressVersion,
  BusinessPartnerPaymentCard,
  BusinessPartnerRelationship,
  BusinessPartnerRole,
  Customer,
  CustomerCompany,
  CustomerCompanyText,
  CustomerPartnerFunction,
  CustomerSalesArea,
  CustomerSalesAreaText,
  CustomerTaxGrouping,
  CustomerText,
  Supplier,
  SupplierCompany,
  SupplierCompanyText,
  SupplierPartnerFunc,
  SupplierPurchasingOrganization,
  SupplierPurchasingOrganizationText,
  SupplierText,
} from "./helpers";

const SyncBusinessPartner = async () => {
  const pending = await strapi
    .query("api::business-partner-staging.business-partner-staging")
    .findOne({ where: { staging_status: "PENDING" } });

  if (!pending) {
    // Skip further processing if no PENDING record exists
    return;
  }

  const { id, documentId, locale, data } = pending;

  try {
    await strapi.db
      .connection("business_partner_stagings")
      .where({ id })
      .update({
        staging_status: "IN_PROCESS",
      });

    await strapi.db.transaction(async ({ onCommit }) => {
      // Sync Business Partner Data
      if (data?.business_partner) {
        if (!data.business_partner.locale) {
          data.business_partner.locale = locale;
        }
        await BusinessPartner(data.business_partner);
      }

      // Sync Business Partner Address Data
      if (Array.isArray(data?.business_partner_address)) {
        for (const addressData of data.business_partner_address) {
          if (!addressData?.locale) {
            addressData.locale = locale;
          }
          await BusinessPartnerAddress(addressData);
        }
      }

      // Sync Business Partner Role Data
      if (Array.isArray(data?.business_partner_role)) {
        for (const role of data.business_partner_role) {
          if (!role?.locale) {
            role.locale = locale;
          }
          await BusinessPartnerRole(role);
        }
      }

      // Sync Business Partner Relationship Data
      if (Array.isArray(data?.bp_relationship)) {
        for (const relationship of data.bp_relationship) {
          if (!relationship?.locale) {
            relationship.locale = locale;
          }
          await BusinessPartnerRelationship(relationship);
        }
      }

      // Sync Business Partner Contact Data
      if (Array.isArray(data?.business_partner_contact)) {
        for (const contactData of data.business_partner_contact) {
          if (!contactData?.locale) {
            contactData.locale = locale;
          }
          await BusinessPartnerContact(contactData);
        }
      }

      // Sync Business Partner Contact To Address Data
      if (Array.isArray(data?.bp_contact_to_address)) {
        for (const addressData of data.bp_contact_to_address) {
          if (!addressData?.locale) {
            addressData.locale = locale;
          }
          await BusinessPartnerContactToAddress(addressData);
        }
      }

      // Sync Business Partner Contact To Func And Depts Data
      if (Array.isArray(data?.bp_contact_to_func_and_depts)) {
        for (const funcDeptsData of data.bp_contact_to_func_and_depts) {
          if (!funcDeptsData?.locale) {
            funcDeptsData.locale = locale;
          }
          await BusinessPartnerContactToFuncAndDepts(funcDeptsData);
        }
      }

      // Sync Business Partner International Address Version Data
      if (Array.isArray(data?.bp_international_address_version)) {
        for (const intlAddressVersionData of data.bp_international_address_version) {
          if (!intlAddressVersionData?.locale) {
            intlAddressVersionData.locale = locale;
          }
          await BusinessPartnerInternationalAddressVersion(
            intlAddressVersionData
          );
        }
      }

      // Sync Business Partner Bank Data
      if (Array.isArray(data?.business_partner_bank)) {
        for (const bankData of data.business_partner_bank) {
          if (!bankData?.locale) {
            bankData.locale = locale;
          }
          await BusinessPartnerBank(bankData);
        }
      }

      // Sync Business Partner Identification Data
      if (Array.isArray(data?.business_partner_identification)) {
        for (const identity of data.business_partner_identification) {
          if (!identity?.locale) {
            identity.locale = locale;
          }
          await BusinessPartnerIdentification(identity);
        }
      }

      // Sync Business Partner PaymentCard Data
      if (Array.isArray(data?.bp_payment_card)) {
        for (const pc of data.bp_payment_card) {
          if (!pc?.locale) {
            pc.locale = locale;
          }
          await BusinessPartnerPaymentCard(pc);
        }
      }

      // Sync Business Partner Credit Worthiness Data
      if (data?.bp_credit_worthinesses) {
        if (!data.bp_credit_worthinesses.locale) {
          data.bp_credit_worthinesses.locale = locale;
        }
        await BusinessPartnerCreditWorthiness(data.bp_credit_worthinesses);
      }

      // Sync Business Partner Address-Dependent International Location Number (ILN) Data
      if (Array.isArray(data?.bp_addr_depdnt_intl_loc_numbers)) {
        for (const bpiln of data.bp_addr_depdnt_intl_loc_numbers) {
          if (!bpiln?.locale) {
            bpiln.locale = locale;
          }
          await BPAddrDepdntIntlLocNumber(bpiln);
        }
      }

      // Sync Business Partner Address Usage Data
      if (Array.isArray(data?.bp_address_usages)) {
        for (const bpAddressUsage of data.bp_address_usages) {
          if (!bpAddressUsage?.locale) {
            bpAddressUsage.locale = locale;
          }
          await BusinessPartnerAddressUsage(bpAddressUsage);
        }
      }

      // Sync Customer Data
      if (data?.customer) {
        if (!data.customer.locale) {
          data.customer.locale = locale;
        }
        await Customer(data.customer);
      }

      // Sync Customer Company Data
      if (Array.isArray(data?.customer_company)) {
        for (const company of data.customer_company) {
          if (!company?.locale) {
            company.locale = locale;
          }
          await CustomerCompany(company);
        }
      }

      // Sync Customer Company Text Data
      if (Array.isArray(data?.customer_company_text)) {
        for (const companyText of data.customer_company_text) {
          if (!companyText?.locale) {
            companyText.locale = locale;
          }
          await CustomerCompanyText(companyText);
        }
      }

      // Sync Customer Partner Function Data
      if (Array.isArray(data?.customer_partner_function)) {
        for (const customerCF of data.customer_partner_function) {
          if (!customerCF?.locale) {
            customerCF.locale = locale;
          }
          await CustomerPartnerFunction(customerCF);
        }
      }

      // Sync Customer Sales Area Data
      if (Array.isArray(data?.customer_sales_area)) {
        for (const customerSA of data.customer_sales_area) {
          if (!customerSA?.locale) {
            customerSA.locale = locale;
          }
          await CustomerSalesArea(customerSA);
        }
      }

      // Sync Customer Sales Area Text Data
      if (Array.isArray(data?.customer_sales_area_text)) {
        for (const customerSAT of data.customer_sales_area_text) {
          if (!customerSAT?.locale) {
            customerSAT.locale = locale;
          }
          await CustomerSalesAreaText(customerSAT);
        }
      }

      // Sync Customer Tax Grouping Data
      if (Array.isArray(data?.customer_tax_grouping)) {
        for (const customerTG of data.customer_tax_grouping) {
          if (!customerTG?.locale) {
            customerTG.locale = locale;
          }
          await CustomerTaxGrouping(customerTG);
        }
      }

      // Sync Customer Text Data
      if (Array.isArray(data?.customer_text)) {
        for (const customerText of data.customer_text) {
          if (!customerText?.locale) {
            customerText.locale = locale;
          }
          await CustomerText(customerText);
        }
      }

      // Sync Supplier Data
      if (data?.supplier) {
        if (!data.supplier.locale) {
          data.supplier.locale = locale;
        }
        await Supplier(data.supplier);
      }

      // Sync Supplier Company Data
      if (Array.isArray(data?.supplier_company)) {
        for (const supplierCompany of data.supplier_company) {
          if (!supplierCompany?.locale) {
            supplierCompany.locale = locale;
          }
          await SupplierCompany(supplierCompany);
        }
      }

      // Sync Supplier Company Text Data
      if (Array.isArray(data?.supplier_company_text)) {
        for (const supplierCompanyText of data.supplier_company_text) {
          if (!supplierCompanyText?.locale) {
            supplierCompanyText.locale = locale;
          }
          await SupplierCompanyText(supplierCompanyText);
        }
      }

      // Sync Supplier Text Data
      if (Array.isArray(data?.supplier_text)) {
        console.log("data?.supplier_text", data?.supplier_text);
        for (const supplierText of data.supplier_text) {
          if (!supplierText?.locale) {
            supplierText.locale = locale;
          }
          await SupplierText(supplierText);
        }
      }

      // Sync Supplier Purchasing Partner Functions Data
      if (Array.isArray(data?.supplier_partner_funcs)) {
        for (const supplierPartnerFunc of data.supplier_partner_funcs) {
          if (!supplierPartnerFunc?.locale) {
            supplierPartnerFunc.locale = locale;
          }
          await SupplierPartnerFunc(supplierPartnerFunc);
        }
      }

      // Sync Supplier Purchasing Organization Data
      if (Array.isArray(data?.supplier_purchasing_orgs)) {
        for (const supplierPO of data.supplier_purchasing_orgs) {
          if (!supplierPO?.locale) {
            supplierPO.locale = locale;
          }
          await SupplierPurchasingOrganization(supplierPO);
        }
      }

      // SyncSupplier Purchasing Organization Text Data
      if (Array.isArray(data?.supplier_purchasing_org_texts)) {
        for (const supplierPOT of data.supplier_purchasing_org_texts) {
          if (!supplierPOT?.locale) {
            supplierPOT.locale = locale;
          }
          await SupplierPurchasingOrganizationText(supplierPOT);
        }
      }

      // Runs only if transaction commits successfully
      onCommit(async () => {
        await strapi.db
          .connection("business_partner_stagings")
          .where({ staging_status: "IN_PROCESS" })
          .del();
      });
    });
  } catch (error) {
    console.error("Transaction failed: ", error);
    await strapi.db
      .connection("business_partner_stagings")
      .insert({
        id,
        document_id: documentId,
        data,
        locale,
        staging_status: "FAILED",
        error_message: error.stack,
        created_at: new Date(),
        updated_at: new Date(),
        published_at: new Date(),
      })
      .onConflict("id") // If id exists, update it
      .merge(["staging_status", "error_message", "updated_at"]); // Update only these fields
  } finally {
    // Throttle recursion slightly to avoid pool saturation
    setTimeout(async () => {
      const inProcess = await strapi.db
        .connection("business_partner_stagings")
        .where({ staging_status: "IN_PROCESS" })
        .first();
      if (!inProcess) {
        SyncBusinessPartner();
      }
    }, 250); // 250ms delay to prevent pool overload
  }
};

export { SyncBusinessPartner };
