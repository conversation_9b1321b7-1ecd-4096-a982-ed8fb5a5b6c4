const chunkArray = (array, size) => {
  return Array.from({ length: Math.ceil(array.length / size) }, (_, index) =>
    array.slice(index * size, index * size + size)
  );
};

const syncBulkCustomerInternal = async (dataBatch) => {
  try {
    if (!dataBatch || dataBatch.length === 0) return;

    const flexGroupIds = dataBatch.map((d) => d.flex_group_id);
    const bpIds = dataBatch.map((d) => d.bp_id);
    const locales = dataBatch.map((d) => d.locale || "en");

    // Step 1: Find existing records
    const existingRecords = await strapi.db
      .query("api::fg-customer-internal.fg-customer-internal")
      .findMany({
        where: {
          flex_group_id: { $in: flexGroupIds },
          bp_id: { $in: bpIds },
          locale: { $in: locales },
        },
        select: ["flex_group_id", "bp_id", "locale"],
      });

    // Step 2: Convert existing records into a Set for quick lookup
    const existingSet = new Set(
      existingRecords.map((r) => `${r.flex_group_id}-${r.bp_id}-${r.locale}`)
    );

    // Step 3: Filter out duplicates before inserting
    const bulkInsertData = dataBatch.filter(
      (d) => !existingSet.has(`${d.flex_group_id}-${d.bp_id}-${d.locale}`)
    );

    if (bulkInsertData.length > 0) {
      await strapi.db
        .query("api::fg-customer-internal.fg-customer-internal")
        .createMany({
          data: bulkInsertData,
        });
    }
  } catch (error) {
    console.error("Bulk insert error in syncBulkCustomerInternal:", error);
  }
};

export { chunkArray, syncBulkCustomerInternal };
