/**
 * crm-opportunity controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";
import { generateUniqueID } from "../../../utils/cpi";

export default factories.createCoreController(
  "api::crm-opportunity.crm-opportunity",
  ({ strapi }) => ({
    async registration(ctx: Context) {
      try {
        const {
          name,
          prospect_party_id,
          primary_contact_party_id,
          origin_type_code,
          expected_revenue_amount,
          expected_revenue_start_date,
          expected_revenue_end_date,
          life_cycle_status_code,
          probability_percent,
          group_code,
          main_employee_responsible_party_id,
          note,
        } = ctx.request.body;

        if (
          !name ||
          !prospect_party_id ||
          !main_employee_responsible_party_id
        ) {
          return ctx.throw(
            400,
            "Missing required fields: name, prospect_party_id, main_employee_responsible_party_id"
          );
        }

        const locale = "en";

        const newOpportunity = await strapi.db.transaction(async () => {
          const opportunity_id = `${await generateUniqueID("crm_opportunity_id_seq")}`;
          let data: any = {
            opportunity_id,
            name,
            prospect_party_id,
            primary_contact_party_id,
            origin_type_code,
            expected_revenue_amount,
            expected_revenue_start_date,
            expected_revenue_end_date,
            life_cycle_status_code,
            probability_percent,
            group_code,
            main_employee_responsible_party_id,
            locale,
          };
          const opportunity = await strapi
            .query("api::crm-opportunity.crm-opportunity")
            .create({
              data,
            });

          data = {
            opportunity_id: opportunity.opportunity_id,
            note,
            is_global_note: true,
            locale,
          };

          await strapi.query("api::crm-note.crm-note").create({
            data,
          });

          return opportunity;
        });

        return ctx.send({
          message: "Opportunity registered successfully",
          data: newOpportunity,
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to register Opportunity: ${error.message}`
        );
      }
    },
  })
);
