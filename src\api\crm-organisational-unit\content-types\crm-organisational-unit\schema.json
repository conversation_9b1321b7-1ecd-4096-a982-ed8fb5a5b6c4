{"kind": "collectionType", "collectionName": "crm_organisational_units", "info": {"singularName": "crm-organisational-unit", "pluralName": "crm-organisational-units", "displayName": "CRM Organisational Unit"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"organisational_unit_id": {"type": "string", "unique": true, "column": {"unique": true}}, "parent_organisational_unit_id": {"type": "string"}, "lifecycle_status_code": {"type": "string"}, "name": {"type": "string"}, "start_date": {"type": "date"}, "end_date": {"type": "date"}, "mark_as_deleted": {"type": "boolean", "default": false}, "crm_org_unit_employees": {"type": "relation", "relation": "oneToMany", "target": "api::crm-organisational-unit-employee.crm-organisational-unit-employee", "mappedBy": "organisational_unit"}, "crm_org_unit_managers": {"type": "relation", "relation": "oneToMany", "target": "api::crm-organisational-unit-manager.crm-organisational-unit-manager", "mappedBy": "organisational_unit"}, "crm_org_unit_functions": {"type": "relation", "relation": "oneToMany", "target": "api::crm-organisational-unit-function.crm-organisational-unit-function", "mappedBy": "organisational_unit"}, "addresses": {"type": "relation", "relation": "oneToMany", "target": "api::crm-organisational-unit-address.crm-organisational-unit-address", "mappedBy": "organisational_unit"}}}