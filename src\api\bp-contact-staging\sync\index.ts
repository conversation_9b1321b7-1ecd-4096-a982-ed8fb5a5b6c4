import { ReplicateBusinessPartnerContact } from "../../../utils/cpi";
import {
  BusinessPartner,
  BusinessPartnerAddress,
  BusinessPartnerContact,
  BusinessPartnerContactToFuncAndDepts,
  BusinessPartnerExtension,
  BusinessPartnerRole,
} from "./helpers";

const SyncBusinessPartnerContact = async (result) => {
  const { id, documentId, locale, data } = result;

  try {
    const isInProcess = await strapi
      .query("api::bp-contact-staging.bp-contact-staging")
      .findOne({ where: { staging_status: "IN_PROCESS" } });

    if (isInProcess) {
      // Skip further processing if an IN_PROCESS record exists
      return;
    }

    await strapi.db.connection("bp_contact_stagings").where({ id }).update({
      staging_status: "IN_PROCESS",
    });

    await strapi.db.transaction(async ({ onCommit }) => {
      // Sync Business Partner Data
      if (data?.business_partner_contact) {
        try {
          // Check if the business partner already exists by bp_id with locale
          const existingPartner = await strapi.db
            .query("api::business-partner.business-partner")
            .findOne({
              where: {
                bp_id: data?.business_partner_contact?.bp_company_id,
                locale,
              },
            });

          if (!existingPartner) {
            throw new Error(
              `Account information not available in system with ${data?.business_partner_contact?.bp_company_id}`
            );
          }
        } catch (error) {
          throw new Error(
            `Unable to synchronize Business Partner Contact ::: ${error.message}`
          );
        }
      }

      // Sync Business Partner Data
      if (data?.business_partner) {
        if (!data.business_partner.locale) {
          data.business_partner.locale = locale;
        }
        await BusinessPartner(data.business_partner);
      }

      // Sync Business Partner Extension Data
      if (data?.business_partner_extension) {
        if (!data.business_partner_extension.locale) {
          data.business_partner_extension.locale = locale;
        }
        await BusinessPartnerExtension(data.business_partner_extension);
      }

      // Sync Business Partner Role Data
      if (data.business_partner_role) {
        if (!data.business_partner_role?.locale) {
          data.business_partner_role.locale = locale;
        }
        await BusinessPartnerRole(data.business_partner_role);
      }

      // Sync Business Partner Contact Data
      if (data.business_partner_contact) {
        if (!data.business_partner_contact?.locale) {
          data.business_partner_contact.locale = locale;
        }
        await BusinessPartnerContact(data.business_partner_contact);
      }

      // Sync Business Partner Contact To Address Data
      if (data.business_partner_address) {
        if (!data.business_partner_address?.locale) {
          data.business_partner_address.locale = locale;
        }
        await BusinessPartnerAddress(data.business_partner_address);
      }

      // Sync Business Partner Contact To Func And Depts Data
      if (data.bp_contact_to_func_and_depts) {
        if (!data.bp_contact_to_func_and_depts?.locale) {
          data.bp_contact_to_func_and_depts.locale = locale;
        }
        await BusinessPartnerContactToFuncAndDepts(
          data.bp_contact_to_func_and_depts
        );
      }

      // Runs only if transaction commits successfully
      onCommit(async () => {
        await strapi.db
          .connection("bp_contact_stagings")
          .where({ staging_status: "IN_PROCESS" })
          .del();

        setImmediate(async () => {
          await ReplicateBusinessPartnerContact(
            data?.business_partner_contact?.bp_company_id
          );
        });

        const pending = await strapi
          .query("api::bp-contact-staging.bp-contact-staging")
          .findOne({ where: { staging_status: "PENDING" } });
        if (pending) {
          await SyncBusinessPartnerContact(pending);
        }
      });
    });
  } catch (error) {
    console.error("Transaction failed: ", error);
    await strapi.db
      .connection("bp_contact_stagings")
      .insert({
        id,
        document_id: documentId,
        data,
        locale,
        staging_status: "FAILED",
        error_message: error.stack,
        created_at: new Date(),
        updated_at: new Date(),
        published_at: new Date(),
      })
      .onConflict("id") // If id exists, update it
      .merge(["staging_status", "error_message", "updated_at"]); // Update only these fields

    const pending = await strapi
      .query("api::bp-contact-staging.bp-contact-staging")
      .findOne({ where: { staging_status: "PENDING" } });
    if (pending) {
      await SyncBusinessPartnerContact(pending);
    }
  }
};

export { SyncBusinessPartnerContact };
