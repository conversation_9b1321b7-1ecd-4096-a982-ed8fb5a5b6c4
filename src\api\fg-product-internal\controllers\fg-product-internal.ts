/**
 * fg-product-internal controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";
import {
  chunkArray,
  fetchProductHierarchy,
  syncBulkProductInternal,
} from "./helpers";

export default factories.createCoreController(
  "api::fg-product-internal.fg-product-internal",
  ({ strapi }) => ({
    async sync(ctx: Context) {
      const setting = await strapi.db.query("api::setting.setting").findOne({
        select: ["id", "is_fgpi_in_progress"],
      });

      if (!setting) {
        return ctx.badRequest("No active sync found.");
      }

      if (setting.is_fgpi_in_progress) {
        return ctx.conflict("Sync is already in progress.");
      }

      // Set flag to prevent multiple requests
      await strapi.db.query("api::setting.setting").update({
        where: { id: setting.id },
        data: { is_fgpi_in_progress: true },
      });

      setImmediate(async () => {
        try {
          console.log("Backup FG Product Internal records...");

          await strapi.db.connection.raw(`
            -- Clear existing backup
            TRUNCATE TABLE fg_product_internal_backups;
            -- Insert backup data
            INSERT INTO fg_product_internal_backups SELECT * FROM fg_product_internals;
          `);

          console.log("DONE Backup FG Product Internal records...");

          console.log("Fetching FG Product Business records...");

          let offset = 0;
          const limit = 1000;

          // Delete Records
          while (true) {
            const products = await strapi.db
              .query("api::fg-product-business.fg-product-business")
              .findMany({
                where: {
                  flex_group_id: { $ne: null },
                  is_all_product: { $eq: false },
                  $or: [
                    { product_id: { $ne: null } },
                    { product_hierarchy: { $ne: null } },
                    { vendor_id: { $ne: null } },
                  ],
                  operand: "DELETE",
                },
                select: [
                  "id",
                  "flex_group_id",
                  "product_id",
                  "product_hierarchy",
                  "vendor_id",
                  "locale",
                ],
                limit,
                offset,
              });

            if (!products.length) break;

            console.log(`Processing batch of ${products.length} records...`);

            const chunkedProducts = chunkArray(products, 500);
            for (const chunk of chunkedProducts) {
              for (const result of chunk) {
                if (result.flex_group_id && result.product_id) {
                  const toDelete = await strapi.db
                    .query("api::fg-product-internal.fg-product-internal")
                    .findMany({
                      where: {
                        flex_group_id: result.flex_group_id,
                        product_id: result.product_id,
                        locale: result.locale || "en",
                      },
                      select: ["id"],
                    });

                  // Perform bulk deletion using the IDs
                  if (toDelete.length > 0) {
                    await strapi.db
                      .query("api::fg-product-internal.fg-product-internal")
                      .deleteMany({
                        where: { id: { $in: toDelete.map(({ id }) => id) } },
                      });
                  }
                }

                // Remove Product Hierarchy
                if (result.flex_group_id && result.product_hierarchy) {
                  const productIds = await fetchProductHierarchy(
                    result.product_hierarchy
                  );

                  const toDelete = await strapi.db
                    .query("api::fg-product-internal.fg-product-internal")
                    .findMany({
                      where: {
                        flex_group_id: result.flex_group_id,
                        product_id: { $in: productIds },
                        locale: result.locale || "en",
                      },
                      select: ["id"],
                    });

                  // Perform bulk deletion using the IDs
                  if (toDelete.length > 0) {
                    await strapi.db
                      .query("api::fg-product-internal.fg-product-internal")
                      .deleteMany({
                        where: { id: { $in: toDelete.map(({ id }) => id) } },
                      });
                  }
                }

                // Remove Flexible Purchase Info Record
                if (result.flex_group_id && result.vendor_id) {
                  const productIds = await strapi.db
                    .query(
                      "api::fg-purchase-info-record.fg-purchase-info-record"
                    )
                    .findMany({
                      where: {
                        vendor_id: result.vendor_id,
                        locale: result.locale || "en",
                      },
                      select: ["product_id"],
                    });

                  const toDelete = await strapi.db
                    .query("api::fg-product-internal.fg-product-internal")
                    .findMany({
                      where: {
                        flex_group_id: result.flex_group_id,
                        product_id: {
                          $in: productIds.map((p) => p.product_id),
                        },
                        locale: result.locale || "en",
                      },
                      select: ["id"],
                    });

                  // Perform bulk deletion using the IDs
                  if (toDelete.length > 0) {
                    await strapi.db
                      .query("api::fg-product-internal.fg-product-internal")
                      .deleteMany({
                        where: { id: { $in: toDelete.map(({ id }) => id) } },
                      });
                  }
                }

                if (result?.id) {
                  await strapi.db
                    .query("api::fg-product-business.fg-product-business")
                    .delete({
                      where: { id: result.id },
                    });
                }
              }
            }

            offset += limit;
          }

          console.log(
            "Initial batch deletion completed. Processing batch insert data..."
          );

          offset = 0;

          while (true) {
            const products = await strapi.db
              .query("api::fg-product-business.fg-product-business")
              .findMany({
                where: {
                  flex_group_id: { $ne: null },
                  product_id: { $ne: null },
                  is_all_product: { $eq: false },
                  $or: [{ operand: "ADD" }, { operand: "UPDATE" }],
                },
                select: ["id", "flex_group_id", "product_id", "locale"],
                limit,
                offset,
              });

            if (!products.length) break;

            console.log(`Processing batch of ${products.length} records...`);

            const chunkedProducts = chunkArray(products, 500);
            for (const chunk of chunkedProducts) {
              await syncBulkProductInternal(chunk).catch((error) =>
                console.error("Bulk sync error:", error)
              );
            }

            offset += limit;
          }

          console.log(
            "Initial batch insert completed. Processing hierarchical data..."
          );

          offset = 0;

          while (true) {
            const hierarchicalProducts = await strapi.db
              .query("api::fg-product-business.fg-product-business")
              .findMany({
                where: {
                  flex_group_id: { $ne: null },
                  product_hierarchy: { $ne: null },
                  is_all_product: { $eq: false },
                  $or: [{ operand: "ADD" }, { operand: "UPDATE" }],
                },
                select: ["id", "flex_group_id", "product_hierarchy", "locale"],
                limit,
                offset,
              });

            if (!hierarchicalProducts.length) break;

            console.log(
              `Processing hierarchical batch of ${hierarchicalProducts.length} records...`
            );

            const bulkInsertData = [];
            for (const data of hierarchicalProducts) {
              try {
                const productIds = await fetchProductHierarchy(
                  data.product_hierarchy
                );
                console.log(
                  `Processing hierarchical ${productIds.length} entries found`
                );
                for (const product_id of productIds) {
                  bulkInsertData.push({
                    flex_group_id: data.flex_group_id,
                    product_id,
                    locale: data.locale || "en",
                  });
                }
              } catch (error) {
                console.error(
                  "Error processing product hierarchy record:",
                  error
                );
              }
            }

            if (bulkInsertData.length > 0) {
              const chunkedBulkData = chunkArray(bulkInsertData, 500);
              for (const chunk of chunkedBulkData) {
                await syncBulkProductInternal(chunk).catch((error) =>
                  console.error("Bulk insert error:", error)
                );
              }
            }

            offset += limit;
          }

          console.log(
            "Initial batch insert completed for product hierarchical data. Processing Flexible Purchase Info Record data..."
          );

          offset = 0;

          while (true) {
            const vendorFGs = await strapi.db
              .query("api::fg-product-business.fg-product-business")
              .findMany({
                where: {
                  flex_group_id: { $ne: null },
                  vendor_id: { $ne: null },
                  is_all_product: { $eq: false },
                  $or: [{ operand: "ADD" }, { operand: "UPDATE" }],
                },
                select: ["id", "flex_group_id", "vendor_id", "locale"],
                limit,
                offset,
              });

            if (!vendorFGs.length) break;

            console.log(
              `Processing Flexible Vendor batch of ${vendorFGs.length} records...`
            );

            const bulkInsertData = [];
            for (const data of vendorFGs) {
              try {
                const productIds = await strapi.db
                  .query("api::fg-purchase-info-record.fg-purchase-info-record")
                  .findMany({
                    where: {
                      vendor_id: data.vendor_id,
                      locale: data.locale || "en",
                    },
                    select: ["product_id"],
                  });
                console.log(
                  `Processing Flexible Purchase Info Record ${productIds.length} entries found`
                );
                for (const product of productIds) {
                  bulkInsertData.push({
                    flex_group_id: data.flex_group_id,
                    product_id: product.product_id,
                    locale: data.locale || "en",
                  });
                }
              } catch (error) {
                console.error(
                  "Error processing Flexible Purchase Info Record:",
                  error
                );
              }
            }

            if (bulkInsertData.length > 0) {
              const chunkedBulkData = chunkArray(bulkInsertData, 500);
              for (const chunk of chunkedBulkData) {
                await syncBulkProductInternal(chunk).catch((error) =>
                  console.error("Bulk insert error:", error)
                );
              }
            }

            offset += limit;
          }
        } catch (error) {
          console.error("Sync error:", error);
        } finally {
          // Reset flag to allow next sync
          await strapi.db.query("api::setting.setting").update({
            where: { id: setting.id },
            data: { is_fgpi_in_progress: false },
          });

          // Insert Data into Customer Product Internal Table
          await strapi.db.connection.raw(`
            -- Clear existing data
            TRUNCATE TABLE fg_customer_product_internals;
            -- Insert new data
            INSERT INTO fg_customer_product_internals (flex_group_id, product_id, locale, bp_id)
            SELECT 
              fpi.flex_group_id,
              fpi.product_id,
              fpi.locale,
              fci.bp_id
            FROM fg_product_internals fpi
            JOIN LATERAL (
              SELECT fci.bp_id
              FROM fg_customer_internals fci
              WHERE fci.flex_group_id = fpi.flex_group_id
              ORDER BY fci.id
              LIMIT 1
            ) fci ON true;
          `);
        }
      });

      return ctx.send({ message: "Sync started." });
    },
    async checkRestrictedProductInternals(ctx: Context) {
      const { customer_id, materials }: any = ctx.request.body;

      if (!customer_id || !materials) {
        return ctx.throw(400, "customer_id and materials are required.");
      }

      if (!Array.isArray(materials)) {
        return ctx.throw(400, "Materials must be array");
      }

      const batchSize = 500;
      let isFirstItem = true;

      ctx.set("Content-Type", "application/json");
      ctx.set("Transfer-Encoding", "chunked");
      ctx.status = 200;

      const { PassThrough } = require("stream");
      const stream = new PassThrough();
      ctx.body = stream;

      const processChunks = async (queryName: string, whereCondition: any) => {
        let offset = 0;
        let hasMore = true;

        while (hasMore) {
          const results = await strapi.db.query(queryName).findMany({
            where: whereCondition,
            offset,
            limit: batchSize,
          });

          if (!results.length) {
            hasMore = false;
            break;
          }

          for (const result of results) {
            const flexGroupId = result.flex_group_id;

            const fgCtrlMain = await strapi.db
              .query("api::fg-control-main.fg-control-main")
              .findOne({
                where: { flex_group_id: flexGroupId },
              });

            const row = {
              Customer: result?.bp_id,
              Material: result?.product_id,
              Restriction: fgCtrlMain?.flex_group_type === "REST" ? "X" : "",
            };

            if (!isFirstItem) stream.write(",");
            isFirstItem = false;

            stream.write(JSON.stringify(row));
          }

          offset += batchSize;
        }
      };

      (async () => {
        stream.write('{"materials": [');

        await processChunks(
          "api::fg-customer-product-internal.fg-customer-product-internal",
          {
            bp_id: customer_id,
            product_id: { $in: materials.map((m: any) => m.Material) },
          }
        );

        stream.write("]}");
        stream.end();
      })().catch((err) => {
        console.error("Streaming error:", err);
        stream.end();
      });
    },
    async restoreProductInternalData(ctx: Context) {
      try {
        console.log("Restore FG Product Internal records...");

        await strapi.db.connection.raw(`
            -- Clear existing backup
            TRUNCATE TABLE fg_product_internals;
            -- Insert backup data
            INSERT INTO fg_product_internals SELECT * FROM fg_product_internal_backups;
          `);

        console.log("DONE Restore FG Product Internal records...");

        return ctx.send({ message: "Restore done." });
      } catch (error) {
        console.log("Error Restore FG Product Internal records...", error);
      }
    },
  })
);
