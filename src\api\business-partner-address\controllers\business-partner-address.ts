/**
 * business-partner-address controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";

export default factories.createCoreController(
  "api::business-partner-address.business-partner-address",
  ({ strapi }) => ({
    async registration(ctx: Context) {
      try {
        const {
          bp_id,
          email_address,
          fax_number,
          website_url,
          phone_number,
          house_number,
          additional_street_prefix_name,
          additional_street_suffix_name,
          street_name,
          city_name,
          country,
          county_code,
          postal_code,
          region,
          is_default_address,
        } = ctx.request.body;

        if (!bp_id) {
          return ctx.throw(400, "bp_id required");
        }

        const locale = "en";

        const newBP = await strapi.db.transaction(async () => {
          let data: any = {
            bp_id,
            house_number,
            additional_street_prefix_name,
            additional_street_suffix_name,
            street_name,
            city_name,
            country,
            county_code,
            postal_code,
            region,
            locale,
          };

          const address = await strapi
            .query("api::business-partner-address.business-partner-address")
            .create({ data });

          if (is_default_address) {
            await strapi.db
              .query("api::bp-address-usage.bp-address-usage")
              .deleteMany({
                where: { bp_id, address_usage: "XXDEFAULT" },
              });
            data = {
              bp_id,
              address_usage: "XXDEFAULT",
              bp_address_id: address.bp_address_id,
              validity_start_date: new Date(),
              validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
              locale,
            };
            await strapi
              .query("api::bp-address-usage.bp-address-usage")
              .create({ data });
          }

          data = {
            email_address,
            business_partner_address: {
              connect: [address.id],
            },
            locale,
          };

          await strapi
            .query("api::bp-email-address.bp-email-address")
            .create({ data });

          data = {
            fax_number,
            business_partner_address: {
              connect: [address.id],
            },
            locale,
          };

          await strapi
            .query("api::bp-fax-number.bp-fax-number")
            .create({ data });

          data = {
            website_url,
            business_partner_address: {
              connect: [address.id],
            },
            locale,
          };

          await strapi
            .query("api::bp-home-page-url.bp-home-page-url")
            .create({ data });

          data = {
            phone_number,
            business_partner_address: {
              connect: [address.id],
            },
            locale,
          };

          await strapi
            .query("api::bp-phone-number.bp-phone-number")
            .create({ data });

          return address;
        });

        return ctx.send({
          message: "Business partner address registered successfully",
          data: newBP,
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to register business-partner: ${error.message}`
        );
      }
    },
    async save(ctx: Context) {
      try {
        const { documentId } = ctx.params;
        const {
          email_address,
          fax_number,
          website_url,
          phone_number,
          house_number,
          additional_street_prefix_name,
          additional_street_suffix_name,
          street_name,
          city_name,
          country,
          county_code,
          postal_code,
          region,
          is_default_address,
        } = ctx.request.body;

        if (!documentId) {
          return ctx.throw(
            400,
            "Business Partner address document id is missing in URL param"
          );
        }

        const updateBP = await strapi.db.transaction(async () => {
          const address = await strapi.db
            .query("api::business-partner-address.business-partner-address")
            .findOne({
              where: {
                documentId: documentId,
              },
            });

          if (address) {
            await strapi
              .query("api::business-partner-address.business-partner-address")
              .update({
                where: { id: address.id },
                data: {
                  house_number,
                  additional_street_prefix_name,
                  additional_street_suffix_name,
                  street_name,
                  city_name,
                  country,
                  county_code,
                  postal_code,
                  region,
                },
              });

            if (is_default_address) {
              await strapi.db
                .query("api::bp-address-usage.bp-address-usage")
                .deleteMany({
                  where: { bp_id: address.bp_id, address_usage: "XXDEFAULT" },
                });

              await strapi
                .query("api::bp-address-usage.bp-address-usage")
                .create({
                  data: {
                    bp_id: address.bp_id,
                    address_usage: "XXDEFAULT",
                    bp_address_id: address.bp_address_id,
                    validity_start_date: new Date(),
                    validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
                    locale: address.locale,
                  },
                });
            } else {
              await strapi.db
                .query("api::bp-address-usage.bp-address-usage")
                .deleteMany({
                  where: {
                    bp_address_id: address.bp_address_id,
                    address_usage: "XXDEFAULT",
                  },
                });
            }

            await strapi
              .query("api::bp-email-address.bp-email-address")
              .update({
                where: { business_partner_address: address.id },
                data: { email_address },
              });

            await strapi.query("api::bp-fax-number.bp-fax-number").update({
              where: { business_partner_address: address.id },
              data: { fax_number },
            });
            await strapi
              .query("api::bp-home-page-url.bp-home-page-url")
              .update({
                where: { business_partner_address: address.id },
                data: { website_url },
              });

            await strapi.query("api::bp-phone-number.bp-phone-number").update({
              where: { business_partner_address: address.id },
              data: { phone_number },
            });
          }

          return address;
        });

        return ctx.send({
          message: "Business partner address save successfully",
          data: updateBP,
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to register business-partner: ${error.message}`
        );
      }
    },
  })
);
